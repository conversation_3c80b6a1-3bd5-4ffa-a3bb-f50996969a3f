/**
* @license Angular v11.0.0-next.6+162.sha-170af07
* (c) 2010-2020 Google LLC. https://angular.io/
* License: MIT
*/
!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */!function(e){var t=e.performance;function n(e){t&&t.mark&&t.mark(e)}function r(e,n){t&&t.measure&&t.measure(e,n)}n("Zone");var o=e.__Zone_symbol_prefix||"__zone_symbol__";function a(e){return o+e}var i=!0===e[a("forceDuplicateZoneCheck")];if(e.Zone){if(i||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}var c=function(){function t(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,t)}return t.assertZonePatched=function(){if(e.Promise!==D.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")},Object.defineProperty(t,"root",{get:function(){for(var e=t.current;e.parent;)e=e.parent;return e},enumerable:!1,configurable:!0}),Object.defineProperty(t,"current",{get:function(){return j.zone},enumerable:!1,configurable:!0}),Object.defineProperty(t,"currentTask",{get:function(){return z},enumerable:!1,configurable:!0}),t.__load_patch=function(o,a){if(D.hasOwnProperty(o)){if(i)throw Error("Already loaded patch: "+o)}else if(!e["__Zone_disable_"+o]){var c="Zone:"+o;n(c),D[o]=a(e,t,C),r(c,c)}},Object.defineProperty(t.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),t.prototype.get=function(e){var t=this.getZoneWith(e);if(t)return t._properties[e]},t.prototype.getZoneWith=function(e){for(var t=this;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null},t.prototype.fork=function(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)},t.prototype.wrap=function(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);var n=this._zoneDelegate.intercept(this,e,t),r=this;return function(){return r.runGuarded(n,this,arguments,t)}},t.prototype.run=function(e,t,n,r){j={parent:j,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,r)}finally{j=j.parent}},t.prototype.runGuarded=function(e,t,n,r){void 0===t&&(t=null),j={parent:j,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{j=j.parent}},t.prototype.runTask=function(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||m).name+"; Execution: "+this.name+")");if(e.state!==b||e.type!==Z&&e.type!==P){var r=e.state!=E;r&&e._transitionTo(E,T),e.runCount++;var o=z;z=e,j={parent:j,zone:this};try{e.type==P&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{e.state!==b&&e.state!==S&&(e.type==Z||e.data&&e.data.isPeriodic?r&&e._transitionTo(T,E):(e.runCount=0,this._updateTaskCount(e,-1),r&&e._transitionTo(b,E,b))),j=j.parent,z=o}}},t.prototype.scheduleTask=function(e){if(e.zone&&e.zone!==this)for(var t=this;t;){if(t===e.zone)throw Error("can not reschedule task to "+this.name+" which is descendants of the original zone "+e.zone.name);t=t.parent}e._transitionTo(k,b);var n=[];e._zoneDelegates=n,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(S,k,b),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===n&&this._updateTaskCount(e,1),e.state==k&&e._transitionTo(T,k),e},t.prototype.scheduleMicroTask=function(e,t,n,r){return this.scheduleTask(new f(O,e,t,n,r,void 0))},t.prototype.scheduleMacroTask=function(e,t,n,r,o){return this.scheduleTask(new f(P,e,t,n,r,o))},t.prototype.scheduleEventTask=function(e,t,n,r,o){return this.scheduleTask(new f(Z,e,t,n,r,o))},t.prototype.cancelTask=function(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||m).name+"; Execution: "+this.name+")");e._transitionTo(w,T,E);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(S,w),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(b,w),e.runCount=0,e},t.prototype._updateTaskCount=function(e,t){var n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(var r=0;r<n.length;r++)n[r]._updateTaskCount(e.type,t)},t}();c.__symbol__=a;var s,l={name:"",onHasTask:function(e,t,n,r){return e.hasTask(n,r)},onScheduleTask:function(e,t,n,r){return e.scheduleTask(n,r)},onInvokeTask:function(e,t,n,r,o,a){return e.invokeTask(n,r,o,a)},onCancelTask:function(e,t,n,r){return e.cancelTask(n,r)}},u=function(){function e(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this.zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;var r=n&&n.onHasTask;(r||t&&t._hasTaskZS)&&(this._hasTaskZS=r?n:l,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=e,n.onScheduleTask||(this._scheduleTaskZS=l,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),n.onInvokeTask||(this._invokeTaskZS=l,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),n.onCancelTask||(this._cancelTaskZS=l,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}return e.prototype.fork=function(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new c(e,t)},e.prototype.intercept=function(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t},e.prototype.invoke=function(e,t,n,r,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,r,o):t.apply(n,r)},e.prototype.handleError=function(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)},e.prototype.scheduleTask=function(e,t){var n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),(n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t))||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=O)throw new Error("Task is missing scheduleFn.");y(t)}return n},e.prototype.invokeTask=function(e,t,n,r){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,r):t.callback.apply(n,r)},e.prototype.cancelTask=function(e,t){var n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n},e.prototype.hasTask=function(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}},e.prototype._updateTaskCount=function(e,t){var n=this._taskCounts,r=n[e],o=n[e]=r+t;if(o<0)throw new Error("More tasks executed then were scheduled.");0!=r&&0!=o||this.hasTask(this.zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})},e}(),f=function(){function t(n,r,o,a,i,c){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=n,this.source=r,this.data=a,this.scheduleFn=i,this.cancelFn=c,!o)throw new Error("callback is not defined");this.callback=o;var s=this;this.invoke=n===Z&&a&&a.useG?t.invokeTask:function(){return t.invokeTask.call(e,s,this,arguments)}}return t.invokeTask=function(e,t,n){e||(e=this),I++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==I&&_(),I--}},Object.defineProperty(t.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.cancelScheduleRequest=function(){this._transitionTo(b,k)},t.prototype._transitionTo=function(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(this.type+" '"+this.source+"': can not transition to '"+e+"', expecting state '"+t+"'"+(n?" or '"+n+"'":"")+", was '"+this._state+"'.");this._state=e,e==b&&(this._zoneDelegates=null)},t.prototype.toString=function(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)},t.prototype.toJSON=function(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}},t}(),p=a("setTimeout"),h=a("Promise"),d=a("then"),v=[],g=!1;function y(t){if(0===I&&0===v.length)if(s||e[h]&&(s=e[h].resolve(0)),s){var n=s[d];n||(n=s.then),n.call(s,_)}else e[p](_,0);t&&v.push(t)}function _(){if(!g){for(g=!0;v.length;){var e=v;v=[];for(var t=0;t<e.length;t++){var n=e[t];try{n.zone.runTask(n,null,null)}catch(e){C.onUnhandledError(e)}}}C.microtaskDrainDone(),g=!1}}var m={name:"NO ZONE"},b="notScheduled",k="scheduling",T="scheduled",E="running",w="canceling",S="unknown",O="microTask",P="macroTask",Z="eventTask",D={},C={symbol:a,currentZoneFrame:function(){return j},onUnhandledError:M,microtaskDrainDone:M,scheduleMicroTask:y,showUncaughtError:function(){return!c[a("ignoreConsoleErrorUncaughtError")]},patchEventTarget:function(){return[]},patchOnProperties:M,patchMethod:function(){return M},bindArguments:function(){return[]},patchThen:function(){return M},patchMacroTask:function(){return M},patchEventPrototype:function(){return M},isIEOrEdge:function(){return!1},getGlobalObjects:function(){},ObjectDefineProperty:function(){return M},ObjectGetOwnPropertyDescriptor:function(){},ObjectCreate:function(){},ArraySlice:function(){return[]},patchClass:function(){return M},wrapWithCurrentZone:function(){return M},filterProperties:function(){return[]},attachOriginToPatched:function(){return M},_redefineProperty:function(){return M},patchCallbacks:function(){return M}},j={parent:null,zone:new c(null,null)},z=null,I=0;function M(){}r("Zone","Zone"),e.Zone=c}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global);var e=Object.getOwnPropertyDescriptor,t=Object.defineProperty,n=Object.getPrototypeOf,r=Object.create,o=Array.prototype.slice,a=Zone.__symbol__("addEventListener"),i=Zone.__symbol__("removeEventListener"),c=Zone.__symbol__("");
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function s(e,t){return Zone.current.wrap(e,t)}function l(e,t,n,r,o){return Zone.current.scheduleMacroTask(e,t,n,r,o)}var u=Zone.__symbol__,f="undefined"!=typeof window,p=f?window:void 0,h=f&&p||"object"==typeof self&&self||global,d=[null];function v(e,t){for(var n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=s(e[n],t+"_"+n));return e}function g(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}var y="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,_=!("nw"in h)&&void 0!==h.process&&"[object process]"==={}.toString.call(h.process),m=!_&&!y&&!(!f||!p.HTMLElement),b=void 0!==h.process&&"[object process]"==={}.toString.call(h.process)&&!y&&!(!f||!p.HTMLElement),k={},T=function(e){if(e=e||h.event){var t=k[e.type];t||(t=k[e.type]=u("ON_PROPERTY"+e.type));var n,r=this||e.target||h,o=r[t];if(m&&r===p&&"error"===e.type){var a=e;!0===(n=o&&o.call(this,a.message,a.filename,a.lineno,a.colno,a.error))&&e.preventDefault()}else null==(n=o&&o.apply(this,arguments))||n||e.preventDefault();return n}};function E(n,r,o){var a=e(n,r);if(!a&&o&&e(o,r)&&(a={enumerable:!0,configurable:!0}),a&&a.configurable){var i=u("on"+r+"patched");if(!n.hasOwnProperty(i)||!n[i]){delete a.writable,delete a.value;var c=a.get,s=a.set,l=r.substr(2),f=k[l];f||(f=k[l]=u("ON_PROPERTY"+l)),a.set=function(e){var t=this;t||n!==h||(t=h),t&&(t[f]&&t.removeEventListener(l,T),s&&s.apply(t,d),"function"==typeof e?(t[f]=e,t.addEventListener(l,T,!1)):t[f]=null)},a.get=function(){var e=this;if(e||n!==h||(e=h),!e)return null;var t=e[f];if(t)return t;if(c){var o=c&&c.call(this);if(o)return a.set.call(this,o),"function"==typeof e.removeAttribute&&e.removeAttribute(r),o}return null},t(n,r,a),n[i]=!0}}}function w(e,t,n){if(t)for(var r=0;r<t.length;r++)E(e,"on"+t[r],n);else{var o=[];for(var a in e)"on"==a.substr(0,2)&&o.push(a);for(var i=0;i<o.length;i++)E(e,o[i],n)}}var S=u("originalInstance");function O(e){var n=h[e];if(n){h[u(e)]=n,h[e]=function(){var t=v(arguments,e);switch(t.length){case 0:this[S]=new n;break;case 1:this[S]=new n(t[0]);break;case 2:this[S]=new n(t[0],t[1]);break;case 3:this[S]=new n(t[0],t[1],t[2]);break;case 4:this[S]=new n(t[0],t[1],t[2],t[3]);break;default:throw new Error("Arg list too long.")}},D(h[e],n);var r,o=new n((function(){}));for(r in o)"XMLHttpRequest"===e&&"responseBlob"===r||function(n){"function"==typeof o[n]?h[e].prototype[n]=function(){return this[S][n].apply(this[S],arguments)}:t(h[e].prototype,n,{set:function(t){"function"==typeof t?(this[S][n]=s(t,e+"."+n),D(this[S][n],t)):this[S][n]=t},get:function(){return this[S][n]}})}(r);for(r in n)"prototype"!==r&&n.hasOwnProperty(r)&&(h[e][r]=n[r])}}function P(t,r,o){for(var a=t;a&&!a.hasOwnProperty(r);)a=n(a);!a&&t[r]&&(a=t);var i=u(r),c=null;if(a&&!(c=a[i])&&(c=a[i]=a[r],g(a&&e(a,r)))){var s=o(c,i,r);a[r]=function(){return s(this,arguments)},D(a[r],c)}return c}function Z(e,t,n){var r=null;function o(e){var t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},r.apply(t.target,t.args),e}r=P(e,t,(function(e){return function(t,r){var a=n(t,r);return a.cbIdx>=0&&"function"==typeof r[a.cbIdx]?l(a.name,r[a.cbIdx],a,o):e.apply(t,r)}}))}function D(e,t){e[u("OriginalDelegate")]=t}var C=!1,j=!1;function z(){if(C)return j;C=!0;try{var e=p.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(j=!0)}catch(e){}return j}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */Zone.__load_patch("ZoneAwarePromise",(function(e,t,n){var r=Object.getOwnPropertyDescriptor,o=Object.defineProperty,a=n.symbol,i=[],c=!0===e[a("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],s=a("Promise"),l=a("then");n.onUnhandledError=function(e){if(n.showUncaughtError()){var t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=function(){for(var e=function(){var e=i.shift();try{e.zone.runGuarded((function(){if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){!function r(e){n.onUnhandledError(e);try{var r=t[u];"function"==typeof r&&r.call(this,e)}catch(e){}}(e)}};i.length;)e()};var u=a("unhandledPromiseRejectionHandler");function f(e){return e&&e.then}function p(e){return e}function h(e){return O.reject(e)}var d=a("state"),v=a("value"),g=a("finally"),y=a("parentPromiseValue"),_=a("parentPromiseState");function m(e,t){return function(n){try{k(e,t,n)}catch(t){k(e,!1,t)}}}var b=a("currentTaskTrace");function k(e,r,a){var s,l=(s=!1,function e(t){return function(){s||(s=!0,t.apply(null,arguments))}});if(e===a)throw new TypeError("Promise resolved with itself");if(null===e[d]){var u=null;try{"object"!=typeof a&&"function"!=typeof a||(u=a&&a.then)}catch(t){return l((function(){k(e,!1,t)}))(),e}if(!1!==r&&a instanceof O&&a.hasOwnProperty(d)&&a.hasOwnProperty(v)&&null!==a[d])E(a),k(e,a[d],a[v]);else if(!1!==r&&"function"==typeof u)try{u.call(a,l(m(e,r)),l(m(e,!1)))}catch(t){l((function(){k(e,!1,t)}))()}else{e[d]=r;var f=e[v];if(e[v]=a,e[g]===g&&!0===r&&(e[d]=e[_],e[v]=e[y]),!1===r&&a instanceof Error){var p=t.currentTask&&t.currentTask.data&&t.currentTask.data.__creationTrace__;p&&o(a,b,{configurable:!0,enumerable:!1,writable:!0,value:p})}for(var h=0;h<f.length;)w(e,f[h++],f[h++],f[h++],f[h++]);if(0==f.length&&0==r){e[d]=0;var T=a;try{throw new Error("Uncaught (in promise): "+function S(e){return e&&e.toString===Object.prototype.toString?(e.constructor&&e.constructor.name||"")+": "+JSON.stringify(e):e?e.toString():Object.prototype.toString.call(e)}(a)+(a&&a.stack?"\n"+a.stack:""))}catch(e){T=e}c&&(T.throwOriginal=!0),T.rejection=a,T.promise=e,T.zone=t.current,T.task=t.currentTask,i.push(T),n.scheduleMicroTask()}}}return e}var T=a("rejectionHandledHandler");function E(e){if(0===e[d]){try{var n=t[T];n&&"function"==typeof n&&n.call(this,{rejection:e[v],promise:e})}catch(e){}e[d]=!1;for(var r=0;r<i.length;r++)e===i[r].promise&&i.splice(r,1)}}function w(e,t,n,r,o){E(e);var a=e[d],i=a?"function"==typeof r?r:p:"function"==typeof o?o:h;t.scheduleMicroTask("Promise.then",(function(){try{var r=e[v],o=!!n&&g===n[g];o&&(n[y]=r,n[_]=a);var c=t.run(i,void 0,o&&i!==h&&i!==p?[]:[r]);k(n,!0,c)}catch(e){k(n,!1,e)}}),n)}var S=function(){},O=function(){function e(t){if(!(this instanceof e))throw new Error("Must be an instanceof Promise.");this[d]=null,this[v]=[];try{t&&t(m(this,!0),m(this,!1))}catch(e){k(this,!1,e)}}return e.toString=function(){return"function ZoneAwarePromise() { [native code] }"},e.resolve=function(e){return k(new this(null),!0,e)},e.reject=function(e){return k(new this(null),!1,e)},e.race=function(e){var t,n,r=new this((function(e,r){t=e,n=r}));function o(e){t(e)}function a(e){n(e)}for(var i=0,c=e;i<c.length;i++){var s=c[i];f(s)||(s=this.resolve(s)),s.then(o,a)}return r},e.all=function(t){return e.allWithCallback(t)},e.allSettled=function(t){return(this&&this.prototype instanceof e?this:e).allWithCallback(t,{thenCallback:function(e){return{status:"fulfilled",value:e}},errorCallback:function(e){return{status:"rejected",reason:e}}})},e.allWithCallback=function(e,t){for(var n,r,o=new this((function(e,t){n=e,r=t})),a=2,i=0,c=[],s=function(e){f(e)||(e=l.resolve(e));var o=i;try{e.then((function(e){c[o]=t?t.thenCallback(e):e,0==--a&&n(c)}),(function(e){t?(c[o]=t.errorCallback(e),0==--a&&n(c)):r(e)}))}catch(e){r(e)}a++,i++},l=this,u=0,p=e;u<p.length;u++)s(p[u]);return 0==(a-=2)&&n(c),o},Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"Promise"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,Symbol.species,{get:function(){return e},enumerable:!1,configurable:!0}),e.prototype.then=function(n,r){var o=this.constructor[Symbol.species];o&&"function"==typeof o||(o=this.constructor||e);var a=new o(S),i=t.current;return null==this[d]?this[v].push(i,a,n,r):w(this,i,a,n,r),a},e.prototype.catch=function(e){return this.then(null,e)},e.prototype.finally=function(n){var r=this.constructor[Symbol.species];r&&"function"==typeof r||(r=e);var o=new r(S);o[g]=g;var a=t.current;return null==this[d]?this[v].push(a,o,n,n):w(this,a,o,n,n),o},e}();O.resolve=O.resolve,O.reject=O.reject,O.race=O.race,O.all=O.all;var Z=e[s]=e.Promise;e.Promise=O;var D=a("thenPatched");function C(e){var t=e.prototype,n=r(t,"then");if(!n||!1!==n.writable&&n.configurable){var o=t.then;t[l]=o,e.prototype.then=function(e,t){var n=this;return new O((function(e,t){o.call(n,e,t)})).then(e,t)},e[D]=!0}}return n.patchThen=C,Z&&(C(Z),P(e,"fetch",(function(e){return function t(e){return function(t,n){var r=e.apply(t,n);if(r instanceof O)return r;var o=r.constructor;return o[D]||C(o),r}}(e)}))),Promise[t.__symbol__("uncaughtPromiseErrors")]=i,O})),
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Zone.__load_patch("toString",(function(e){var t=Function.prototype.toString,n=u("OriginalDelegate"),r=u("Promise"),o=u("Error"),a=function a(){if("function"==typeof this){var i=this[n];if(i)return"function"==typeof i?t.call(i):Object.prototype.toString.call(i);if(this===Promise){var c=e[r];if(c)return t.call(c)}if(this===Error){var s=e[o];if(s)return t.call(s)}}return t.call(this)};a[n]=t,Function.prototype.toString=a;var i=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":i.call(this)}}));
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var I=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){I=!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){I=!1}var R={useG:!0},L={},N={},x=new RegExp("^"+c+"(\\w+)(true|false)$"),F=u("propagationStopped");function H(e,t){var n=(t?t(e):e)+"false",r=(t?t(e):e)+"true",o=c+n,a=c+r;L[e]={},L[e].false=o,L[e].true=a}function A(e,t,r){var o=r&&r.add||"addEventListener",a=r&&r.rm||"removeEventListener",i=r&&r.listeners||"eventListeners",s=r&&r.rmAll||"removeAllListeners",l=u(o),f="."+o+":",p=function(e,t,n){if(!e.isRemoved){var r=e.callback;"object"==typeof r&&r.handleEvent&&(e.callback=function(e){return r.handleEvent(e)},e.originalDelegate=r),e.invoke(e,t,[n]);var o=e.options;o&&"object"==typeof o&&o.once&&t[a].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,o)}},h=function(t){if(t=t||e.event){var n=this||t.target||e,r=n[L[t.type].false];if(r)if(1===r.length)p(r[0],n,t);else for(var o=r.slice(),a=0;a<o.length&&(!t||!0!==t[F]);a++)p(o[a],n,t)}},d=function(t){if(t=t||e.event){var n=this||t.target||e,r=n[L[t.type].true];if(r)if(1===r.length)p(r[0],n,t);else for(var o=r.slice(),a=0;a<o.length&&(!t||!0!==t[F]);a++)p(o[a],n,t)}};function v(t,r){if(!t)return!1;var p=!0;r&&void 0!==r.useG&&(p=r.useG);var v=r&&r.vh,g=!0;r&&void 0!==r.chkDup&&(g=r.chkDup);var y=!1;r&&void 0!==r.rt&&(y=r.rt);for(var m=t;m&&!m.hasOwnProperty(o);)m=n(m);if(!m&&t[o]&&(m=t),!m)return!1;if(m[l])return!1;var b,k=r&&r.eventNameToString,T={},E=m[l]=m[o],w=m[u(a)]=m[a],S=m[u(i)]=m[i],O=m[u(s)]=m[s];function P(e,t){return!I&&"object"==typeof e&&e?!!e.capture:I&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?Object.assign(Object.assign({},e),{passive:!0}):e:{passive:!0}:e}r&&r.prepend&&(b=m[u(r.prepend)]=m[r.prepend]);var Z=p?function(e){if(!T.isExisting)return E.call(T.target,T.eventName,T.capture?d:h,T.options)}:function(e){return E.call(T.target,T.eventName,e.invoke,T.options)},C=p?function(e){if(!e.isRemoved){var t=L[e.eventName],n=void 0;t&&(n=t[e.capture?"true":"false"]);var r=n&&e.target[n];if(r)for(var o=0;o<r.length;o++)if(r[o]===e){r.splice(o,1),e.isRemoved=!0,0===r.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return w.call(e.target,e.eventName,e.capture?d:h,e.options)}:function(e){return w.call(e.target,e.eventName,e.invoke,e.options)},j=r&&r.diff?r.diff:function(e,t){var n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},z=Zone[u("UNPATCHED_EVENTS")],M=e[u("PASSIVE_EVENTS")],F=function(t,n,o,a,i,c){return void 0===i&&(i=!1),void 0===c&&(c=!1),function(){var s=this||e,l=arguments[0];r&&r.transferEventName&&(l=r.transferEventName(l));var u=arguments[1];if(!u)return t.apply(this,arguments);if(_&&"uncaughtException"===l)return t.apply(this,arguments);var f=!1;if("function"!=typeof u){if(!u.handleEvent)return t.apply(this,arguments);f=!0}if(!v||v(t,u,s,arguments)){var h=I&&!!M&&-1!==M.indexOf(l),d=P(arguments[2],h);if(z)for(var y=0;y<z.length;y++)if(l===z[y])return h?t.call(s,l,u,d):t.apply(this,arguments);var m=!!d&&("boolean"==typeof d||d.capture),b=!(!d||"object"!=typeof d)&&d.once,E=Zone.current,w=L[l];w||(H(l,k),w=L[l]);var S,O=w[m?"true":"false"],Z=s[O],D=!1;if(Z){if(D=!0,g)for(y=0;y<Z.length;y++)if(j(Z[y],u))return}else Z=s[O]=[];var C=s.constructor.name,x=N[C];x&&(S=x[l]),S||(S=C+n+(k?k(l):l)),T.options=d,b&&(T.options.once=!1),T.target=s,T.capture=m,T.eventName=l,T.isExisting=D;var F=p?R:void 0;F&&(F.taskData=T);var A=E.scheduleEventTask(S,u,F,o,a);return T.target=null,F&&(F.taskData=null),b&&(d.once=!0),(I||"boolean"!=typeof A.options)&&(A.options=d),A.target=s,A.capture=m,A.eventName=l,f&&(A.originalDelegate=u),c?Z.unshift(A):Z.push(A),i?s:void 0}}};return m[o]=F(E,f,Z,C,y),b&&(m.prependListener=F(b,".prependListener:",(function(e){return b.call(T.target,T.eventName,e.invoke,T.options)}),C,y,!0)),m[a]=function(){var t=this||e,n=arguments[0];r&&r.transferEventName&&(n=r.transferEventName(n));var o=arguments[2],a=!!o&&("boolean"==typeof o||o.capture),i=arguments[1];if(!i)return w.apply(this,arguments);if(!v||v(w,i,t,arguments)){var s,l=L[n];l&&(s=l[a?"true":"false"]);var u=s&&t[s];if(u)for(var f=0;f<u.length;f++){var p=u[f];if(j(p,i)){if(u.splice(f,1),p.isRemoved=!0,0===u.length&&(p.allRemoved=!0,t[s]=null,"string"==typeof n)){var h=c+"ON_PROPERTY"+n;t[h]=null}return p.zone.cancelTask(p),y?t:void 0}}return w.apply(this,arguments)}},m[i]=function(){var t=this||e,n=arguments[0];r&&r.transferEventName&&(n=r.transferEventName(n));for(var o=[],a=B(t,k?k(n):n),i=0;i<a.length;i++){var c=a[i],s=c.originalDelegate?c.originalDelegate:c.callback;o.push(s)}return o},m[s]=function(){var t=this||e,n=arguments[0];if(n){r&&r.transferEventName&&(n=r.transferEventName(n));var o=L[n];if(o){var i=o.false,c=o.true,l=t[i],u=t[c];if(l){var f=l.slice();for(d=0;d<f.length;d++)this[a].call(this,n,(p=f[d]).originalDelegate?p.originalDelegate:p.callback,p.options)}if(u)for(f=u.slice(),d=0;d<f.length;d++){var p;this[a].call(this,n,(p=f[d]).originalDelegate?p.originalDelegate:p.callback,p.options)}}}else{for(var h=Object.keys(t),d=0;d<h.length;d++){var v=h[d],g=x.exec(v),_=g&&g[1];_&&"removeListener"!==_&&this[s].call(this,_)}this[s].call(this,"removeListener")}if(y)return this},D(m[o],E),D(m[a],w),O&&D(m[s],O),S&&D(m[i],S),!0}for(var g=[],y=0;y<t.length;y++)g[y]=v(t[y],r);return g}function B(e,t){if(!t){var n=[];for(var r in e){var o=x.exec(r),a=o&&o[1];if(a&&(!t||a===t)){var i=e[r];if(i)for(var c=0;c<i.length;c++)n.push(i[c])}}return n}var s=L[t];s||(H(t),s=L[t]);var l=e[s.false],u=e[s.true];return l?u?l.concat(u):l.slice():u?u.slice():[]}function G(e,t){var n=e.Event;n&&n.prototype&&t.patchMethod(n.prototype,"stopImmediatePropagation",(function(e){return function(t,n){t[F]=!0,e&&e.apply(t,n)}}))}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function W(e,t,n,r,o){var a=Zone.__symbol__(r);if(!t[a]){var i=t[a]=t[r];t[r]=function(a,c,s){return c&&c.prototype&&o.forEach((function(t){var o=n+"."+r+"::"+t,a=c.prototype;if(a.hasOwnProperty(t)){var i=e.ObjectGetOwnPropertyDescriptor(a,t);i&&i.value?(i.value=e.wrapWithCurrentZone(i.value,o),e._redefineProperty(c.prototype,t,i)):a[t]&&(a[t]=e.wrapWithCurrentZone(a[t],o))}else a[t]&&(a[t]=e.wrapWithCurrentZone(a[t],o))})),i.call(t,a,c,s)},e.attachOriginToPatched(t[r],i)}}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */var q,U,V,X,Y,J=["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],K=["encrypted","waitingforkey","msneedkey","mozinterruptbegin","mozinterruptend"],Q=["load"],$=["blur","error","focus","load","resize","scroll","messageerror"],ee=["bounce","finish","start"],te=["loadstart","progress","abort","error","load","progress","timeout","loadend","readystatechange"],ne=["upgradeneeded","complete","abort","success","error","blocked","versionchange","close"],re=["close","error","open","message"],oe=["error","message"],ae=["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"].concat(["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],["autocomplete","autocompleteerror"],["toggle"],["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],J,["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"]);function ie(e,t,n){if(!n||0===n.length)return t;var r=n.filter((function(t){return t.target===e}));if(!r||0===r.length)return t;var o=r[0].ignoreProperties;return t.filter((function(e){return-1===o.indexOf(e)}))}function ce(e,t,n,r){e&&w(e,ie(e,t,n),r)}function se(e,t){if((!_||b)&&!Zone[e.symbol("patchEvents")]){var r="undefined"!=typeof WebSocket,o=t.__Zone_ignore_on_properties;if(m){var a=window,i=function c(){try{var e=p.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}()?[{target:a,ignoreProperties:["error"]}]:[];ce(a,ae.concat(["messageerror"]),o?o.concat(i):o,n(a)),ce(Document.prototype,ae,o),void 0!==a.SVGElement&&ce(a.SVGElement.prototype,ae,o),ce(Element.prototype,ae,o),ce(HTMLElement.prototype,ae,o),ce(HTMLMediaElement.prototype,K,o),ce(HTMLFrameSetElement.prototype,J.concat($),o),ce(HTMLBodyElement.prototype,J.concat($),o),ce(HTMLFrameElement.prototype,Q,o),ce(HTMLIFrameElement.prototype,Q,o);var s=a.HTMLMarqueeElement;s&&ce(s.prototype,ee,o);var l=a.Worker;l&&ce(l.prototype,oe,o)}var u=t.XMLHttpRequest;u&&ce(u.prototype,te,o);var f=t.XMLHttpRequestEventTarget;f&&ce(f&&f.prototype,te,o),"undefined"!=typeof IDBIndex&&(ce(IDBIndex.prototype,ne,o),ce(IDBRequest.prototype,ne,o),ce(IDBOpenDBRequest.prototype,ne,o),ce(IDBDatabase.prototype,ne,o),ce(IDBTransaction.prototype,ne,o),ce(IDBCursor.prototype,ne,o)),r&&ce(WebSocket.prototype,re,o)}}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function le(){q=Zone.__symbol__,U=Object[q("defineProperty")]=Object.defineProperty,V=Object[q("getOwnPropertyDescriptor")]=Object.getOwnPropertyDescriptor,X=Object.create,Y=q("unconfigurables"),Object.defineProperty=function(e,t,n){if(fe(e,t))throw new TypeError("Cannot assign to read only property '"+t+"' of "+e);var r=n.configurable;return"prototype"!==t&&(n=pe(e,t,n)),he(e,t,n,r)},Object.defineProperties=function(e,t){return Object.keys(t).forEach((function(n){Object.defineProperty(e,n,t[n])})),e},Object.create=function(e,t){return"object"!=typeof t||Object.isFrozen(t)||Object.keys(t).forEach((function(n){t[n]=pe(e,n,t[n])})),X(e,t)},Object.getOwnPropertyDescriptor=function(e,t){var n=V(e,t);return n&&fe(e,t)&&(n.configurable=!1),n}}function ue(e,t,n){var r=n.configurable;return he(e,t,n=pe(e,t,n),r)}function fe(e,t){return e&&e[Y]&&e[Y][t]}function pe(e,t,n){return Object.isFrozen(n)||(n.configurable=!0),n.configurable||(e[Y]||Object.isFrozen(e)||U(e,Y,{writable:!0,value:{}}),e[Y]&&(e[Y][t]=!0)),n}function he(e,t,n,r){try{return U(e,t,n)}catch(i){if(!n.configurable)throw i;void 0===r?delete n.configurable:n.configurable=r;try{return U(e,t,n)}catch(r){var o=!1;if("createdCallback"!==t&&"attachedCallback"!==t&&"detachedCallback"!==t&&"attributeChangedCallback"!==t||(o=!0),!o)throw r;var a=null;try{a=JSON.stringify(n)}catch(e){a=n.toString()}console.log("Attempting to configure '"+t+"' with descriptor '"+a+"' on object '"+e+"' and got error, giving up: "+r)}}}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function de(e,t){var n=t.getGlobalObjects(),r=n.eventNames,o=n.globalSources,a=n.zoneSymbolEventNames,i=n.TRUE_STR,c=n.FALSE_STR,s=n.ZONE_SYMBOL_PREFIX,l="ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket".split(","),u=[],f=e.wtf,p="Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video".split(",");f?u=p.map((function(e){return"HTML"+e+"Element"})).concat(l):e.EventTarget?u.push("EventTarget"):u=l;for(var h=e.__Zone_disable_IE_check||!1,d=e.__Zone_enable_cross_context_check||!1,v=t.isIEOrEdge(),g="function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }",y={MSPointerCancel:"pointercancel",MSPointerDown:"pointerdown",MSPointerEnter:"pointerenter",MSPointerHover:"pointerhover",MSPointerLeave:"pointerleave",MSPointerMove:"pointermove",MSPointerOut:"pointerout",MSPointerOver:"pointerover",MSPointerUp:"pointerup"},_=0;_<r.length;_++){var m=s+((w=r[_])+c),b=s+(w+i);a[w]={},a[w][c]=m,a[w][i]=b}for(_=0;_<p.length;_++)for(var k=p[_],T=o[k]={},E=0;E<r.length;E++){var w;T[w=r[E]]=k+".addEventListener:"+w}var S=[];for(_=0;_<u.length;_++){var O=e[u[_]];S.push(O&&O.prototype)}return t.patchEventTarget(e,S,{vh:function(e,t,n,r){if(!h&&v){if(d)try{var o;if("[object FunctionWrapper]"===(o=t.toString())||o==g)return e.apply(n,r),!1}catch(t){return e.apply(n,r),!1}else if("[object FunctionWrapper]"===(o=t.toString())||o==g)return e.apply(n,r),!1}else if(d)try{t.toString()}catch(t){return e.apply(n,r),!1}return!0},transferEventName:function(e){return y[e]||e}}),Zone[t.symbol("patchEventTarget")]=!!e.EventTarget,!0}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function ve(e,t){var n=e.getGlobalObjects();if((!n.isNode||n.isMix)&&!function r(e,t){var n=e.getGlobalObjects();if((n.isBrowser||n.isMix)&&!e.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype,"onclick")&&"undefined"!=typeof Element){var r=e.ObjectGetOwnPropertyDescriptor(Element.prototype,"onclick");if(r&&!r.configurable)return!1;if(r){e.ObjectDefineProperty(Element.prototype,"onclick",{enumerable:!0,configurable:!0,get:function(){return!0}});var o=!!document.createElement("div").onclick;return e.ObjectDefineProperty(Element.prototype,"onclick",r),o}}var a=t.XMLHttpRequest;if(!a)return!1;var i=a.prototype,c=e.ObjectGetOwnPropertyDescriptor(i,"onreadystatechange");if(c)return e.ObjectDefineProperty(i,"onreadystatechange",{enumerable:!0,configurable:!0,get:function(){return!0}}),o=!!(l=new a).onreadystatechange,e.ObjectDefineProperty(i,"onreadystatechange",c||{}),o;var s=e.symbol("fake");e.ObjectDefineProperty(i,"onreadystatechange",{enumerable:!0,configurable:!0,get:function(){return this[s]},set:function(e){this[s]=e}});var l=new a,u=function(){};return l.onreadystatechange=u,o=l[s]===u,l.onreadystatechange=null,o}(e,t)){var o="undefined"!=typeof WebSocket;!function a(e){for(var t=e.getGlobalObjects().eventNames,n=e.symbol("unbound"),r=function(r){var o=t[r],a="on"+o;self.addEventListener(o,(function(t){var r,o,i=t.target;for(o=i?i.constructor.name+"."+a:"unknown."+a;i;)i[a]&&!i[a][n]&&((r=e.wrapWithCurrentZone(i[a],o))[n]=i[a],i[a]=r),i=i.parentElement}),!0)},o=0;o<t.length;o++)r(o)}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */(e),e.patchClass("XMLHttpRequest"),o&&function i(e,t){var n=e.getGlobalObjects(),r=n.ADD_EVENT_LISTENER_STR,o=n.REMOVE_EVENT_LISTENER_STR,a=t.WebSocket;t.EventTarget||e.patchEventTarget(t,[a.prototype]),t.WebSocket=function(t,n){var i,c,s=arguments.length>1?new a(t,n):new a(t),l=e.ObjectGetOwnPropertyDescriptor(s,"onmessage");return l&&!1===l.configurable?(i=e.ObjectCreate(s),c=s,[r,o,"send","close"].forEach((function(t){i[t]=function(){var n=e.ArraySlice.call(arguments);if(t===r||t===o){var a=n.length>0?n[0]:void 0;if(a){var c=Zone.__symbol__("ON_PROPERTY"+a);s[c]=i[c]}}return s[t].apply(s,n)}}))):i=s,e.patchOnProperties(i,["close","error","message","open"],c),i};var i=t.WebSocket;for(var c in a)i[c]=a[c]}(e,t),Zone[e.symbol("patchEvents")]=!0}}Zone.__load_patch("util",(function(n,a,i){i.patchOnProperties=w,i.patchMethod=P,i.bindArguments=v,i.patchMacroTask=Z;var l=a.__symbol__("BLACK_LISTED_EVENTS"),u=a.__symbol__("UNPATCHED_EVENTS");n[u]&&(n[l]=n[u]),n[l]&&(a[l]=a[u]=n[l]),i.patchEventPrototype=G,i.patchEventTarget=A,i.isIEOrEdge=z,i.ObjectDefineProperty=t,i.ObjectGetOwnPropertyDescriptor=e,i.ObjectCreate=r,i.ArraySlice=o,i.patchClass=O,i.wrapWithCurrentZone=s,i.filterProperties=ie,i.attachOriginToPatched=D,i._redefineProperty=Object.defineProperty,i.patchCallbacks=W,i.getGlobalObjects=function(){return{globalSources:N,zoneSymbolEventNames:L,eventNames:ae,isBrowser:m,isMix:b,isNode:_,TRUE_STR:"true",FALSE_STR:"false",ZONE_SYMBOL_PREFIX:c,ADD_EVENT_LISTENER_STR:"addEventListener",REMOVE_EVENT_LISTENER_STR:"removeEventListener"}}})),
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function(e){var t=e.__Zone_symbol_prefix||"__zone_symbol__";e[function n(e){return t+e}("legacyPatch")]=function(){var t=e.Zone;t.__load_patch("defineProperty",(function(e,t,n){n._redefineProperty=ue,le()})),t.__load_patch("registerElement",(function(e,t,n){!function r(e,t){var n=t.getGlobalObjects();(n.isBrowser||n.isMix)&&"registerElement"in e.document&&t.patchCallbacks(t,document,"Document","registerElement",["createdCallback","attachedCallback","detachedCallback","attributeChangedCallback"])}(e,n)})),t.__load_patch("EventTargetLegacy",(function(e,t,n){de(e,n),ve(n,e)}))}}("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{});
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
var ge=u("zoneTask");function ye(e,t,n,r){var o=null,a=null;n+=r;var i={};function c(t){var n=t.data;return n.args[0]=function r(){try{t.invoke.apply(this,arguments)}finally{t.data&&t.data.isPeriodic||("number"==typeof n.handleId?delete i[n.handleId]:n.handleId&&(n.handleId[ge]=null))}},n.handleId=o.apply(e,n.args),t}function s(t){return a.call(e,t.data.handleId)}o=P(e,t+=r,(function(n){return function(o,a){if("function"==typeof a[0]){var u=l(t,a[0],{isPeriodic:"Interval"===r,delay:"Timeout"===r||"Interval"===r?a[1]||0:void 0,args:a},c,s);if(!u)return u;var f=u.data.handleId;return"number"==typeof f?i[f]=u:f&&(f[ge]=u),f&&f.ref&&f.unref&&"function"==typeof f.ref&&"function"==typeof f.unref&&(u.ref=f.ref.bind(f),u.unref=f.unref.bind(f)),"number"==typeof f||f?f:u}return n.apply(e,a)}})),a=P(e,n,(function(t){return function(n,r){var o,a=r[0];"number"==typeof a?o=i[a]:(o=a&&a[ge])||(o=a),o&&"string"==typeof o.type?"notScheduled"!==o.state&&(o.cancelFn&&o.data.isPeriodic||0===o.runCount)&&("number"==typeof a?delete i[a]:a&&(a[ge]=null),o.zone.cancelTask(o)):t.apply(e,r)}}))}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
function _e(e,t){if(!Zone[t.symbol("patchEventTarget")]){for(var n=t.getGlobalObjects(),r=n.eventNames,o=n.zoneSymbolEventNames,a=n.TRUE_STR,i=n.FALSE_STR,c=n.ZONE_SYMBOL_PREFIX,s=0;s<r.length;s++){var l=r[s],u=c+(l+i),f=c+(l+a);o[l]={},o[l][i]=u,o[l][a]=f}var p=e.EventTarget;if(p&&p.prototype)return t.patchEventTarget(e,[p&&p.prototype]),!0}}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Zone.__load_patch("legacy",(function(e){var t=e[Zone.__symbol__("legacyPatch")];t&&t()})),Zone.__load_patch("timers",(function(e){ye(e,"set","clear","Timeout"),ye(e,"set","clear","Interval"),ye(e,"set","clear","Immediate")})),Zone.__load_patch("requestAnimationFrame",(function(e){ye(e,"request","cancel","AnimationFrame"),ye(e,"mozRequest","mozCancel","AnimationFrame"),ye(e,"webkitRequest","webkitCancel","AnimationFrame")})),Zone.__load_patch("blocking",(function(e,t){for(var n=["alert","prompt","confirm"],r=0;r<n.length;r++)P(e,n[r],(function(n,r,o){return function(r,a){return t.current.run(n,e,a,o)}}))})),Zone.__load_patch("EventTarget",(function(e,t,n){!function r(e,t){t.patchEventPrototype(e,t)}(e,n),_e(e,n);var o=e.XMLHttpRequestEventTarget;o&&o.prototype&&n.patchEventTarget(e,[o.prototype])})),Zone.__load_patch("MutationObserver",(function(e,t,n){O("MutationObserver"),O("WebKitMutationObserver")})),Zone.__load_patch("IntersectionObserver",(function(e,t,n){O("IntersectionObserver")})),Zone.__load_patch("FileReader",(function(e,t,n){O("FileReader")})),Zone.__load_patch("on_property",(function(e,t,n){se(n,e)})),Zone.__load_patch("customElements",(function(e,t,n){!function r(e,t){var n=t.getGlobalObjects();(n.isBrowser||n.isMix)&&e.customElements&&"customElements"in e&&t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(e,n)})),Zone.__load_patch("XHR",(function(e,t){!function n(e){var n=e.XMLHttpRequest;if(n){var h=n.prototype,d=h[a],v=h[i];if(!d){var g=e.XMLHttpRequestEventTarget;if(g){var y=g.prototype;d=y[a],v=y[i]}}var _=P(h,"open",(function(){return function(e,t){return e[o]=0==t[2],e[f]=t[1],_.apply(e,t)}})),m=u("fetchTaskAborting"),b=u("fetchTaskScheduling"),k=P(h,"send",(function(){return function(e,n){if(!0===t.current[b])return k.apply(e,n);if(e[o])return k.apply(e,n);var r={target:e,url:e[f],isPeriodic:!1,args:n,aborted:!1},a=l("XMLHttpRequest.send",w,r,E,S);e&&!0===e[p]&&!r.aborted&&"scheduled"===a.state&&a.invoke()}})),T=P(h,"abort",(function(){return function(e,n){var o=function a(e){return e[r]}(e);if(o&&"string"==typeof o.type){if(null==o.cancelFn||o.data&&o.data.aborted)return;o.zone.cancelTask(o)}else if(!0===t.current[m])return T.apply(e,n)}}))}function E(e){var n=e.data,o=n.target;o[s]=!1,o[p]=!1;var l=o[c];d||(d=o[a],v=o[i]),l&&v.call(o,"readystatechange",l);var u=o[c]=function(){if(o.readyState===o.DONE)if(!n.aborted&&o[s]&&"scheduled"===e.state){var r=o[t.__symbol__("loadfalse")];if(0!==o.status&&r&&r.length>0){var a=e.invoke;e.invoke=function(){for(var r=o[t.__symbol__("loadfalse")],i=0;i<r.length;i++)r[i]===e&&r.splice(i,1);n.aborted||"scheduled"!==e.state||a.call(e)},r.push(e)}else e.invoke()}else n.aborted||!1!==o[s]||(o[p]=!0)};return d.call(o,"readystatechange",u),o[r]||(o[r]=e),k.apply(o,n.args),o[s]=!0,e}function w(){}function S(e){var t=e.data;return t.aborted=!0,T.apply(t.target,t.args)}}(e);var r=u("xhrTask"),o=u("xhrSync"),c=u("xhrListener"),s=u("xhrScheduled"),f=u("xhrURL"),p=u("xhrErrorBeforeScheduled")})),Zone.__load_patch("geolocation",(function(t){t.navigator&&t.navigator.geolocation&&function n(t,r){for(var o=t.constructor.name,a=function(n){var a=r[n],i=t[a];if(i){if(!g(e(t,a)))return"continue";t[a]=function(e){var t=function(){return e.apply(this,v(arguments,o+"."+a))};return D(t,e),t}(i)}},i=0;i<r.length;i++)a(i)}(t.navigator.geolocation,["getCurrentPosition","watchPosition"])})),Zone.__load_patch("PromiseRejectionEvent",(function(e,t){function n(t){return function(n){B(e,t).forEach((function(r){var o=e.PromiseRejectionEvent;if(o){var a=new o(t,{promise:n.promise,reason:n.rejection});r.invoke(a)}}))}}e.PromiseRejectionEvent&&(t[u("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),t[u("rejectionHandledHandler")]=n("rejectionhandled"))}))}));