/* eslint-disable prettier/prettier */
//Desenvolvedor: EDITAR ESTE ARQUIVO APENAS caso você esteja desenvolvendo MFEs.
//O seu MFE deve ser adicionado na variável "listaMFEs" abaixo.
//NÃO é necessário editar a variável "variaveisAmbiente".
//Após editar este arquivo, é necessário que o comando npm start seja executado novamente,
//pois a atualização automática de alterações NÃO funciona para este caso.
const common = require("./environment.common");
const listaMFEs = `[
   {
    "application": "PlataformaCaixa-PosVenda-Prestamista",
    "route": "/seguridade/prestamista/pos-venda",
    "path": "https://posvendahm.caixavidaeprevidencia.extranet.caixa/prestamista/CVP-PlataformaCaixa-PosVenda-Prestamista.js"
  },
  {
    "application": "PlataformaCaixa-PosVenda-Vida",
    "route": "/seguridade/vida/pos-venda",
    "path": "https://posvendahm.caixavidaeprevidencia.extranet.caixa/vida/CVP-PlataformaCaixa-PosVenda-Vida.js"
  },
  {
    "application": "PlataformaCaixa-PosVenda-Previdencia",
    "route": "/seguridade/previdencia/pos-venda",
    "path": "https://posvendahm.caixavidaeprevidencia.extranet.caixa/previdencia/CVP-PlataformaCaixa-PosVenda-Previdencia.js"
  },
  {
    "application": "PlataformaCaixa-Venda-Vida",
    "route": "/seguridade/vida/venda",
    "path": "https://vendahm.caixavidaeprevidencia.extranet.caixa/vida/CVP-PlataformaCaixa-Venda-Vida.js"
  },
  {
    "application":"PlataformaCaixa-Venda-Previdencia",
    "route":"/seguridade/previdencia/venda",
    "path":"https://vendahm.caixavidaeprevidencia.extranet.caixa/previdencia/CVP-PlataformaCaixa-Venda-Previdencia.js"
  },
  {
    "application":"PlataformaCaixa-Venda-Prestamista",
    "route":"/seguridade/prestamista/venda",
    "path":"https://vendahm.caixavidaeprevidencia.extranet.caixa/prestamista/CVP-PlataformaCaixa-Venda-Prestamista.js"
  },
  {
    "application": "caixa-sipnc-assinaturas-des",
    "route": "/assinaturas",
    "path": "https://sipnc-assinaturas-microfront-des.apps.nprd.caixa/main.js"
  }
]`;
const variaveisAmbiente = {
  urlCDN: "https://plataformahm.caixavidaeprevidencia.extranet.caixa",
  urlHost:
    "//plataformahm.caixavidaeprevidencia.extranet.caixa/caixa-sipnc-host.js",
  urlCore:
    "https://plataformahm.caixavidaeprevidencia.extranet.caixa/static/core/caixa-sipnc-core.js",
  urlNavBar:
    "https://plataformahm.caixavidaeprevidencia.extranet.caixa/static/navbar/main-es5.js",
  urlMenuDinamico: "https://plataformahm.caixavidaeprevidencia.extranet.caixa",
  listaMFEs: listaMFEs,
  isBuscarMFEsDinamicos: "false",

  ...common,
};
module.exports = variaveisAmbiente;

/*Legenda:
  urlCDN: endereço do CDN;
  urlHost: endereço do host;
  urlCore: endereço do core; //Editar esta variável quando estiver modificando o core: //localhost:4000;
  urlNavBar: endereço do navbar; //Editar esta variável quando estiver modificando o navbar: //localhost:4201;
  urlMenuDinamico: endereço do backend do menu dinâmico;
  listaMFEs: lista de MFEs a serem carregados pelo host;
  isBuscarMFEsDinamicos: flag que indica se a lista de MFEs a serem carregados virá da variável "listaMFEs" ou
                         do backend do menu dinâmico;
*/
