import { HttpHand<PERSON>, HttpRequest } from '@angular/common/http';
export { clearSession, getSessionItem, removeSessionItem, setSessionItem } from "./session-storage";
export { interceptUtil } from "./http-interceptors/interceptor";
export declare function wrapperLoadFontes(url: string): void;
export declare function logout(): void;
export declare function wrapperIntercept(req: HttpRequest<any>, next: HttpHandler): import("rxjs").Observable<import("@angular/common/http").HttpEvent<any>>;
export declare function wrapperKeyCloak(): string;
export declare function routerRedirectMFE(rotaMFE: string, nomeMfe?: string, breadcrumb?: any): void;
export declare function routerLoadingMFE(loading: boolean): void;
