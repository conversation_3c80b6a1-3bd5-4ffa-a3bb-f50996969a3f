"use strict";var sjclcrp={cipher:{},hash:{},keyexchange:{},mode:{},misc:{},codec:{},exception:{corrupt:function(e){this.toString=function(){return"CORRUPT: "+this.message},this.message=e},invalid:function(e){this.toString=function(){return"INVALID: "+this.message},this.message=e},bug:function(e){this.toString=function(){return"BUG: "+this.message},this.message=e},notReady:function(e){this.toString=function(){return"NOT READY: "+this.message},this.message=e}}};"undefined"!=typeof module&&module.exports&&(module.exports=sjclcrp),"function"==typeof define&&define([],function(){return sjclcrp}); sjclcrp.cipher.aes=function(t){this._tables[0][0][0]||this._precompute();var e,i,s,r,n,c=this._tables[0][4],l=this._tables[1],o=t.length,a=1;if(4!==o&&6!==o&&8!==o)throw new sjclcrp.exception.invalid("invalid aes key size");for(this._key=[r=t.slice(0),n=[]],e=o;4*o+28>e;e++)s=r[e-1],(e%o===0||8===o&&e%o===4)&&(s=c[s>>>24]<<24^c[s>>16&255]<<16^c[s>>8&255]<<8^c[255&s],e%o===0&&(s=s<<8^s>>>24^a<<24,a=a<<1^283*(a>>7))),r[e]=r[e-o]^s;for(i=0;e;i++,e--)s=r[3&i?e:e-4],n[i]=4>=e||4>i?s:l[0][c[s>>>24]]^l[1][c[s>>16&255]]^l[2][c[s>>8&255]]^l[3][c[255&s]]},sjclcrp.cipher.aes.prototype={encrypt:function(t){return this._crypt(t,0)},decrypt:function(t){return this._crypt(t,1)},_tables:[[[],[],[],[],[]],[[],[],[],[],[]]],_precompute:function(){var t,e,i,s,r,n,c,l,o,a=this._tables[0],h=this._tables[1],f=a[4],p=h[4],_=[],u=[];for(t=0;256>t;t++)u[(_[t]=t<<1^283*(t>>7))^t]=t;for(e=i=0;!f[e];e^=s||1,i=u[i]||1)for(c=i^i<<1^i<<2^i<<3^i<<4,c=c>>8^255&c^99,f[e]=c,p[c]=e,n=_[r=_[s=_[e]]],o=16843009*n^65537*r^257*s^16843008*e,l=257*_[c]^16843008*c,t=0;4>t;t++)a[t][e]=l=l<<24^l>>>8,h[t][c]=o=o<<24^o>>>8;for(t=0;5>t;t++)a[t]=a[t].slice(0),h[t]=h[t].slice(0)},_crypt:function(t,e){if(4!==t.length)throw new sjclcrp.exception.invalid("invalid aes block size");var i,s,r,n,c=this._key[e],l=t[0]^c[0],o=t[e?3:1]^c[1],a=t[2]^c[2],h=t[e?1:3]^c[3],f=c.length/4-2,p=4,_=[0,0,0,0],u=this._tables[e],y=u[0],b=u[1],v=u[2],d=u[3],j=u[4];for(n=0;f>n;n++)i=y[l>>>24]^b[o>>16&255]^v[a>>8&255]^d[255&h]^c[p],s=y[o>>>24]^b[a>>16&255]^v[h>>8&255]^d[255&l]^c[p+1],r=y[a>>>24]^b[h>>16&255]^v[l>>8&255]^d[255&o]^c[p+2],h=y[h>>>24]^b[l>>16&255]^v[o>>8&255]^d[255&a]^c[p+3],p+=4,l=i,o=s,a=r;for(n=0;4>n;n++)_[e?3&-n:n]=j[l>>>24]<<24^j[o>>16&255]<<16^j[a>>8&255]<<8^j[255&h]^c[p++],i=l,l=o,o=a,a=h,h=i;return _}}; sjclcrp.bitArray={bitSlice:function(t,r,n){return t=sjclcrp.bitArray._shiftRight(t.slice(r/32),32-(31&r)).slice(1),void 0===n?t:sjclcrp.bitArray.clamp(t,n-r)},extract:function(t,r,n){var i,a=Math.floor(-r-n&31);return i=-32&(r+n-1^r)?t[r/32|0]<<32-a^t[r/32+1|0]>>>a:t[r/32|0]>>>a,i&(1<<n)-1},concat:function(t,r){if(0===t.length||0===r.length)return t.concat(r);var n=t[t.length-1],i=sjclcrp.bitArray.getPartial(n);return 32===i?t.concat(r):sjclcrp.bitArray._shiftRight(r,i,0|n,t.slice(0,t.length-1))},bitLength:function(t){var r,n=t.length;return 0===n?0:(r=t[n-1],32*(n-1)+sjclcrp.bitArray.getPartial(r))},clamp:function(t,r){if(32*t.length<r)return t;t=t.slice(0,Math.ceil(r/32));var n=t.length;return r=31&r,n>0&&r&&(t[n-1]=sjclcrp.bitArray.partial(r,t[n-1]&2147483648>>r-1,1)),t},partial:function(t,r,n){return 32===t?r:(n?0|r:r<<32-t)+1099511627776*t},getPartial:function(t){return Math.round(t/1099511627776)||32},equal:function(t,r){if(sjclcrp.bitArray.bitLength(t)!==sjclcrp.bitArray.bitLength(r))return!1;var n,i=0;for(n=0;n<t.length;n++)i|=t[n]^r[n];return 0===i},_shiftRight:function(t,r,n,i){var a,e,l=0;for(void 0===i&&(i=[]);r>=32;r-=32)i.push(n),n=0;if(0===r)return i.concat(t);for(a=0;a<t.length;a++)i.push(n|t[a]>>>r),n=t[a]<<32-r;return l=t.length?t[t.length-1]:0,e=sjclcrp.bitArray.getPartial(l),i.push(sjclcrp.bitArray.partial(r+e&31,r+e>32?n:i.pop(),1)),i},_xor4:function(t,r){return[t[0]^r[0],t[1]^r[1],t[2]^r[2],t[3]^r[3]]},byteswapM:function(t){var r,n,i=65280;for(r=0;r<t.length;++r)n=t[r],t[r]=n>>>24|n>>>8&i|(n&i)<<8|n<<24;return t}}; sjclcrp.bn=function(t){this.initWith(t)},sjclcrp.bn.prototype={radix:24,maxMul:8,_class:sjclcrp.bn,copy:function(){return new this._class(this)},initWith:function(t){var s,i=0;switch(typeof t){case"object":this.limbs=t.limbs.slice(0);break;case"number":this.limbs=[t],this.normalize();break;case"string":for(t=t.replace(/^0x/,""),this.limbs=[],s=this.radix/4,i=0;i<t.length;i+=s)this.limbs.push(parseInt(t.substring(Math.max(t.length-i-s,0),t.length-i),16));break;default:this.limbs=[0]}return this},equals:function(t){"number"==typeof t&&(t=new this._class(t));var s,i=0;for(this.fullReduce(),t.fullReduce(),s=0;s<this.limbs.length||s<t.limbs.length;s++)i|=this.getLimb(s)^t.getLimb(s);return 0===i},getLimb:function(t){return t>=this.limbs.length?0:this.limbs[t]},greaterEquals:function(t){"number"==typeof t&&(t=new this._class(t));var s,i,e,n=0,r=0;for(s=Math.max(this.limbs.length,t.limbs.length)-1;s>=0;s--)i=this.getLimb(s),e=t.getLimb(s),r|=e-i&~n,n|=i-e&~r;return(r|~n)>>>31},toString:function(){this.fullReduce();var t,s,i="",e=this.limbs;for(t=0;t<this.limbs.length;t++){for(s=e[t].toString(16);t<this.limbs.length-1&&s.length<6;)s="0"+s;i=s+i}return"0x"+i},addM:function(t){"object"!=typeof t&&(t=new this._class(t));var s,i=this.limbs,e=t.limbs;for(s=i.length;s<e.length;s++)i[s]=0;for(s=0;s<e.length;s++)i[s]+=e[s];return this},doubleM:function(){var t,s,i=0,e=this.radix,n=this.radixMask,r=this.limbs;for(t=0;t<r.length;t++)s=r[t],s=s+s+i,r[t]=s&n,i=s>>e;return i&&r.push(i),this},halveM:function(){var t,s,i=0,e=this.radix,n=this.limbs;for(t=n.length-1;t>=0;t--)s=n[t],n[t]=s+i>>1,i=(1&s)<<e;return n[n.length-1]||n.pop(),this},subM:function(t){"object"!=typeof t&&(t=new this._class(t));var s,i=this.limbs,e=t.limbs;for(s=i.length;s<e.length;s++)i[s]=0;for(s=0;s<e.length;s++)i[s]-=e[s];return this},mod:function(t){var s=!this.greaterEquals(new sjclcrp.bn(0));t=new sjclcrp.bn(t).normalize();var i=new sjclcrp.bn(this).normalize(),e=0;for(s&&(i=new sjclcrp.bn(0).subM(i).normalize());i.greaterEquals(t);e++)t.doubleM();for(s&&(i=t.sub(i).normalize());e>0;e--)t.halveM(),i.greaterEquals(t)&&i.subM(t).normalize();return i.trim()},inverseMod:function(t){var s,i,e=new sjclcrp.bn(1),n=new sjclcrp.bn(0),r=new sjclcrp.bn(this),l=new sjclcrp.bn(t),o=1;if(!(1&t.limbs[0]))throw new sjclcrp.exception.invalid("inverseMod: p must be odd");do for(1&r.limbs[0]&&(r.greaterEquals(l)||(s=r,r=l,l=s,s=e,e=n,n=s),r.subM(l),r.normalize(),e.greaterEquals(n)||e.addM(t),e.subM(n)),r.halveM(),1&e.limbs[0]&&e.addM(t),e.normalize(),e.halveM(),i=o=0;i<r.limbs.length;i++)o|=r.limbs[i];while(o);if(!l.equals(1))throw new sjclcrp.exception.invalid("inverseMod: p and x must be relatively prime");return n},add:function(t){return this.copy().addM(t)},sub:function(t){return this.copy().subM(t)},mul:function(t){"number"==typeof t&&(t=new this._class(t));var s,i,e,n=this.limbs,r=t.limbs,l=n.length,o=r.length,h=new this._class,a=h.limbs,u=this.maxMul;for(s=0;s<this.limbs.length+t.limbs.length+1;s++)a[s]=0;for(s=0;l>s;s++){for(e=n[s],i=0;o>i;i++)a[s+i]+=e*r[i];--u||(u=this.maxMul,h.cnormalize())}return h.cnormalize().reduce()},square:function(){return this.mul(this)},power:function(t){"number"==typeof t?t=[t]:void 0!==t.limbs&&(t=t.normalize().limbs);var s,i,e=new this._class(1),n=this;for(s=0;s<t.length;s++)for(i=0;i<this.radix;i++)t[s]&1<<i&&(e=e.mul(n)),n=n.square();return e},mulmod:function(t,s){return this.mod(s).mul(t.mod(s)).mod(s)},powermod:function(t,s){for(var i=new sjclcrp.bn(1),e=new sjclcrp.bn(this),n=new sjclcrp.bn(t);;){if(1&n.limbs[0]&&(i=i.mulmod(e,s)),n.halveM(),n.equals(0))break;e=e.mulmod(e,s)}return i.normalize().reduce()},trim:function(){var t,s=this.limbs;do t=s.pop();while(s.length&&0===t);return s.push(t),this},reduce:function(){return this},fullReduce:function(){return this.normalize()},normalize:function(){var t,s,i,e=0,n=this.placeVal,r=this.ipv,l=this.limbs,o=l.length,h=this.radixMask;for(t=0;o>t||0!==e&&-1!==e;t++)s=(l[t]||0)+e,i=l[t]=s&h,e=(s-i)*r;return-1===e&&(l[t-1]-=n),this},cnormalize:function(){var t,s,i,e=0,n=this.ipv,r=this.limbs,l=r.length,o=this.radixMask;for(t=0;l-1>t;t++)s=r[t]+e,i=r[t]=s&o,e=(s-i)*n;return r[t]+=e,this},toBits:function(t){this.fullReduce(),t=t||this.exponent||this.bitLength();var s=Math.floor((t-1)/24),i=sjclcrp.bitArray,e=(t+7&-8)%this.radix||this.radix,n=[i.partial(e,this.getLimb(s))];for(s--;s>=0;s--)n=i.concat(n,[i.partial(Math.min(this.radix,t),this.getLimb(s))]),t-=this.radix;return n},bitLength:function(){this.fullReduce();for(var t=this.radix*(this.limbs.length-1),s=this.limbs[this.limbs.length-1];s;s>>>=1)t++;return t+7&-8}},sjclcrp.bn.fromBits=function(t){var s=this,i=new s,e=[],n=sjclcrp.bitArray,r=this.prototype,l=Math.min(this.bitLength||4294967296,n.bitLength(t)),o=l%r.radix||r.radix;for(e[0]=n.extract(t,0,o);l>o;o+=r.radix)e.unshift(n.extract(t,o,r.radix));return i.limbs=e,i},sjclcrp.bn.prototype.ipv=1/(sjclcrp.bn.prototype.placeVal=Math.pow(2,sjclcrp.bn.prototype.radix)),sjclcrp.bn.prototype.radixMask=(1<<sjclcrp.bn.prototype.radix)-1,sjclcrp.bn.pseudoMersennePrime=function(t,s){function i(t){this.initWith(t)}var e,n,r,l=i.prototype=new sjclcrp.bn;for(r=l.modOffset=Math.ceil(n=t/l.radix),l.exponent=t,l.offset=[],l.factor=[],l.minOffset=r,l.fullMask=0,l.fullOffset=[],l.fullFactor=[],l.modulus=i.modulus=new sjclcrp.bn(Math.pow(2,t)),l.fullMask=0|-Math.pow(2,t%l.radix),e=0;e<s.length;e++)l.offset[e]=Math.floor(s[e][0]/l.radix-n),l.fullOffset[e]=Math.ceil(s[e][0]/l.radix-n),l.factor[e]=s[e][1]*Math.pow(.5,t-s[e][0]+l.offset[e]*l.radix),l.fullFactor[e]=s[e][1]*Math.pow(.5,t-s[e][0]+l.fullOffset[e]*l.radix),l.modulus.addM(new sjclcrp.bn(Math.pow(2,s[e][0])*s[e][1])),l.minOffset=Math.min(l.minOffset,-l.offset[e]);return l._class=i,l.modulus.cnormalize(),l.reduce=function(){var t,s,i,e,n=this.modOffset,r=this.limbs,l=this.offset,o=this.offset.length,h=this.factor;for(t=this.minOffset;r.length>n;){for(i=r.pop(),e=r.length,s=0;o>s;s++)r[e+l[s]]-=h[s]*i;t--,t||(r.push(0),this.cnormalize(),t=this.minOffset)}return this.cnormalize(),this},l._strongReduce=-1===l.fullMask?l.reduce:function(){var t,s,i=this.limbs,e=i.length-1;if(this.reduce(),e===this.modOffset-1){for(s=i[e]&this.fullMask,i[e]-=s,t=0;t<this.fullOffset.length;t++)i[e+this.fullOffset[t]]-=this.fullFactor[t]*s;this.normalize()}},l.fullReduce=function(){var t,s;for(this._strongReduce(),this.addM(this.modulus),this.addM(this.modulus),this.normalize(),this._strongReduce(),s=this.limbs.length;s<this.modOffset;s++)this.limbs[s]=0;for(t=this.greaterEquals(this.modulus),s=0;s<this.limbs.length;s++)this.limbs[s]-=this.modulus.limbs[s]*t;return this.cnormalize(),this},l.inverse=function(){return this.power(this.modulus.sub(2))},i.fromBits=sjclcrp.bn.fromBits,i};var sbp=sjclcrp.bn.pseudoMersennePrime;sjclcrp.bn.prime={p127:sbp(127,[[0,-1]]),p25519:sbp(255,[[0,-19]]),p192k:sbp(192,[[32,-1],[12,-1],[8,-1],[7,-1],[6,-1],[3,-1],[0,-1]]),p224k:sbp(224,[[32,-1],[12,-1],[11,-1],[9,-1],[7,-1],[4,-1],[1,-1],[0,-1]]),p256k:sbp(256,[[32,-1],[9,-1],[8,-1],[7,-1],[6,-1],[4,-1],[0,-1]]),p192:sbp(192,[[0,-1],[64,-1]]),p224:sbp(224,[[0,1],[96,-1]]),p256:sbp(256,[[0,-1],[96,1],[192,1],[224,-1]]),p384:sbp(384,[[0,-1],[32,1],[96,-1],[128,-1]]),p521:sbp(521,[[0,-1]])},sjclcrp.bn.random=function(t,s){"object"!=typeof t&&(t=new sjclcrp.bn(t));for(var i,e,n=t.limbs.length,r=t.limbs[n-1]+1,l=new sjclcrp.bn;;){do i=sjclcrp.random.randomWords(n,s),i[n-1]<0&&(i[n-1]+=4294967296);while(Math.floor(i[n-1]/r)===Math.floor(4294967296/r));for(i[n-1]%=r,e=0;n-1>e;e++)i[e]&=t.radixMask;if(l.limbs=i,!l.greaterEquals(t))return l}}; void 0===sjclcrp.beware&&(sjclcrp.beware={}),sjclcrp.beware["CBC mode is dangerous because it doesn't protect message integrity."]=function(){sjclcrp.mode.cbc={name:"cbc",encrypt:function(e,t,c,i){if(i&&i.length)throw new sjclcrp.exception.invalid("cbc can't authenticate data");if(128!==sjclcrp.bitArray.bitLength(c))throw new sjclcrp.exception.invalid("cbc iv must be 128 bits");var n,r=sjclcrp.bitArray,l=r._xor4,o=r.bitLength(t),s=0,a=[];if(7&o)throw new sjclcrp.exception.invalid("pkcs#5 padding only works for multiples of a byte");for(n=0;o>=s+128;n+=4,s+=128)c=e.encrypt(l(c,t.slice(n,n+4))),a.splice(n,0,c[0],c[1],c[2],c[3]);return o=16843009*(16-(o>>3&15)),c=e.encrypt(l(c,r.concat(t,[o,o,o,o]).slice(n,n+4))),a.splice(n,0,c[0],c[1],c[2],c[3]),a},decrypt:function(e,t,c,i){if(i&&i.length)throw new sjclcrp.exception.invalid("cbc can't authenticate data");if(128!==sjclcrp.bitArray.bitLength(c))throw new sjclcrp.exception.invalid("cbc iv must be 128 bits");if(127&sjclcrp.bitArray.bitLength(t)||!t.length)throw new sjclcrp.exception.corrupt("cbc ciphertext must be a positive multiple of the block size");var n,r,l,o=sjclcrp.bitArray,s=o._xor4,a=[];for(i=i||[],n=0;n<t.length;n+=4)r=t.slice(n,n+4),l=s(c,e.decrypt(r)),a.splice(n,0,l[0],l[1],l[2],l[3]),c=r;if(r=255&a[n-1],0===r||r>16)throw new sjclcrp.exception.corrupt("pkcs#5 padding corrupt");if(l=16843009*r,!o.equal(o.bitSlice([l,l,l,l],0,8*r),o.bitSlice(a,32*a.length-8*r,32*a.length)))throw new sjclcrp.exception.corrupt("pkcs#5 padding corrupt");return o.bitSlice(a,0,32*a.length-8*r)}}}; sjclcrp.mode.ccm={name:"ccm",encrypt:function(t,c,e,a,n){var r,i,l=c.slice(0),o=sjclcrp.bitArray,m=o.bitLength(e)/8,g=o.bitLength(l)/8;if(n=n||64,a=a||[],7>m)throw new sjclcrp.exception.invalid("ccm: iv must be at least 7 bytes");for(r=2;4>r&&g>>>8*r;r++);return 15-m>r&&(r=15-m),e=o.clamp(e,8*(15-r)),i=sjclcrp.mode.ccm._computeTag(t,c,e,a,n,r),l=sjclcrp.mode.ccm._ctrMode(t,l,e,i,n,r),o.concat(l.data,l.tag)},decrypt:function(t,c,e,a,n){n=n||64,a=a||[];var r,i,l=sjclcrp.bitArray,o=l.bitLength(e)/8,m=l.bitLength(c),g=l.clamp(c,m-n),p=l.bitSlice(c,m-n);if(m=(m-n)/8,7>o)throw new sjclcrp.exception.invalid("ccm: iv must be at least 7 bytes");for(r=2;4>r&&m>>>8*r;r++);if(15-o>r&&(r=15-o),e=l.clamp(e,8*(15-r)),g=sjclcrp.mode.ccm._ctrMode(t,g,e,p,n,r),i=sjclcrp.mode.ccm._computeTag(t,g.data,e,a,n,r),!l.equal(g.tag,i))throw new sjclcrp.exception.corrupt("ccm: tag doesn't match");return g.data},_computeTag:function(t,c,e,a,n,r){var i,l,o,m=[],g=sjclcrp.bitArray,p=g._xor4;if(n/=8,n%2||4>n||n>16)throw new sjclcrp.exception.invalid("ccm: invalid tag length");if(a.length>**********||c.length>**********)throw new sjclcrp.exception.bug("ccm: can't deal with 4GiB or more data");if(i=[g.partial(8,(a.length?64:0)|n-2<<2|r-1)],i=g.concat(i,e),i[3]|=g.bitLength(c)/8,i=t.encrypt(i),a.length)for(l=g.bitLength(a)/8,65279>=l?m=[g.partial(16,l)]:**********>=l&&(m=g.concat([g.partial(16,65534)],[l])),m=g.concat(m,a),o=0;o<m.length;o+=4)i=t.encrypt(p(i,m.slice(o,o+4).concat([0,0,0])));for(o=0;o<c.length;o+=4)i=t.encrypt(p(i,c.slice(o,o+4).concat([0,0,0])));return g.clamp(i,8*n)},_ctrMode:function(t,c,e,a,n,r){var i,l,o,m=sjclcrp.bitArray,g=m._xor4,p=c.length,s=m.bitLength(c);if(o=m.concat([m.partial(8,r-1)],e).concat([0,0,0]).slice(0,4),a=m.bitSlice(g(a,t.encrypt(o)),0,n),!p)return{tag:a,data:[]};for(l=0;p>l;l+=4)o[3]++,i=t.encrypt(o),c[l]^=i[0],c[l+1]^=i[1],c[l+2]^=i[2],c[l+3]^=i[3];return{tag:a,data:m.clamp(c,s)}}}; sjclcrp.codec.base32={_chars:"0123456789abcdefghjkmnpqrstvwxyz",BITS:32,BASE:5,REMAINING:27,fromBits:function(c){var s,e=(sjclcrp.codec.base32.BITS,sjclcrp.codec.base32.BASE),a=sjclcrp.codec.base32.REMAINING,r="",t=0,o=sjclcrp.codec.base32._chars,l=0,i=sjclcrp.bitArray.bitLength(c);for(s=0;r.length*e<=i;)r+=o.charAt((l^c[s]>>>t)>>>a),e>t?(l=c[s]<<e-t,t+=a,s++):(l<<=e,t-=e);return r},toBits:function(c){var s,e,a=sjclcrp.codec.base32.BITS,r=sjclcrp.codec.base32.BASE,t=sjclcrp.codec.base32.REMAINING,o=[],l=0,i=sjclcrp.codec.base32._chars,n=0;for(s=0;s<c.length;s++){if(e=i.indexOf(c.charAt(s)),0>e)throw new sjclcrp.exception.invalid("this isn't base32!");l>t?(l-=t,o.push(n^e>>>l),n=e<<a-l):(l+=r,n^=e<<a-l)}return 56&l&&o.push(sjclcrp.bitArray.partial(56&l,n,1)),o}}; sjclcrp.codec.base64={_chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fromBits:function(s,t,c){var r,e="",i=0,n=sjclcrp.codec.base64._chars,o=0,a=sjclcrp.bitArray.bitLength(s);for(c&&(n=n.substr(0,62)+"-_"),r=0;6*e.length<a;)e+=n.charAt((o^s[r]>>>i)>>>26),6>i?(o=s[r]<<6-i,i+=26,r++):(o<<=6,i-=6);for(;3&e.length&&!t;)e+="=";return e},toBits:function(s,t){s=s.replace(/\s|=/g,"");var c,r,e=[],i=0,n=sjclcrp.codec.base64._chars,o=0;for(t&&(n=n.substr(0,62)+"-_"),c=0;c<s.length;c++){if(r=n.indexOf(s.charAt(c)),0>r)throw new sjclcrp.exception.invalid("this isn't base64!");i>26?(i-=26,e.push(o^r>>>i),o=r<<32-i):(i+=6,o^=r<<32-i)}return 56&i&&e.push(sjclcrp.bitArray.partial(56&i,o,1)),e}},sjclcrp.codec.base64url={fromBits:function(s){return sjclcrp.codec.base64.fromBits(s,1,1)},toBits:function(s){return sjclcrp.codec.base64.toBits(s,1)}}; sjclcrp.codec.bytes={fromBits:function(r){var t,s,i=[],n=sjclcrp.bitArray.bitLength(r);for(t=0;n/8>t;t++)0===(3&t)&&(s=r[t/4]),i.push(s>>>24),s<<=8;return i},toBits:function(r){var t,s=[],i=0;for(t=0;t<r.length;t++)i=i<<8|r[t],3===(3&t)&&(s.push(i),i=0);return 3&t&&s.push(sjclcrp.bitArray.partial(8*(3&t),i)),s}}; sjclcrp.codec.hex={fromBits:function(t){var r,s="";for(r=0;r<t.length;r++)s+=((0|t[r])+0xf00000000000).toString(16).substr(4);return s.substr(0,sjclcrp.bitArray.bitLength(t)/4)},toBits:function(t){var r,s,n=[];for(t=t.replace(/\s|0x/g,""),s=t.length,t+="00000000",r=0;r<t.length;r+=8)n.push(0^parseInt(t.substr(r,8),16));return sjclcrp.bitArray.clamp(n,4*s)}}; sjclcrp.codec.utf8String={fromBits:function(r){var t,e,n="",o=sjclcrp.bitArray.bitLength(r);for(t=0;o/8>t;t++)0===(3&t)&&(e=r[t/4]),n+=String.fromCharCode(e>>>24),e<<=8;return decodeURIComponent(escape(n))},toBits:function(r){r=unescape(encodeURIComponent(r));var t,e=[],n=0;for(t=0;t<r.length;t++)n=n<<8|r.charCodeAt(t),3===(3&t)&&(e.push(n),n=0);return 3&t&&e.push(sjclcrp.bitArray.partial(8*(3&t),n)),e}}; sjclcrp.json={defaults:{v:1,iter:1e3,ks:128,ts:64,mode:"ccm",adata:"",cipher:"aes"},_encrypt:function(e,t,s,c){s=s||{},c=c||{};var i,r,n,a=sjclcrp.json,o=a._add({iv:sjclcrp.random.randomWords(4,0)},a.defaults);if(a._add(o,s),n=o.adata,"string"==typeof o.salt&&(o.salt=sjclcrp.codec.base64.toBits(o.salt)),"string"==typeof o.iv&&(o.iv=sjclcrp.codec.base64.toBits(o.iv)),!sjclcrp.mode[o.mode]||!sjclcrp.cipher[o.cipher]||"string"==typeof e&&o.iter<=100||64!==o.ts&&96!==o.ts&&128!==o.ts||128!==o.ks&&192!==o.ks&&256!==o.ks||o.iv.length<2||o.iv.length>4)throw new sjclcrp.exception.invalid("json encrypt: invalid parameters");return"string"==typeof e?(i=sjclcrp.misc.cachedPbkdf2(e,o),e=i.key.slice(0,o.ks/32),o.salt=i.salt):sjclcrp.ecc&&e instanceof sjclcrp.ecc.elGamal.publicKey&&(i=e.kem(),o.kemtag=i.tag,e=i.key.slice(0,o.ks/32)),"string"==typeof t&&(t=sjclcrp.codec.utf8String.toBits(t)),"string"==typeof n&&(o.adata=n=sjclcrp.codec.utf8String.toBits(n)),r=new sjclcrp.cipher[o.cipher](e),a._add(c,o),c.key=e,o.ct=sjclcrp.mode[o.mode].encrypt(r,t,o.iv,n,o.ts),o},encrypt:function(){var e=sjclcrp.json,t=e._encrypt.apply(e,arguments);return e.encode(t)},_decrypt:function(e,t,s,c){s=s||{},c=c||{};var i,r,n,a=sjclcrp.json,o=a._add(a._add(a._add({},a.defaults),t),s,!0),l=o.adata;if("string"==typeof o.salt&&(o.salt=sjclcrp.codec.base64.toBits(o.salt)),"string"==typeof o.iv&&(o.iv=sjclcrp.codec.base64.toBits(o.iv)),!sjclcrp.mode[o.mode]||!sjclcrp.cipher[o.cipher]||"string"==typeof e&&o.iter<=100||64!==o.ts&&96!==o.ts&&128!==o.ts||128!==o.ks&&192!==o.ks&&256!==o.ks||!o.iv||o.iv.length<2||o.iv.length>4)throw new sjclcrp.exception.invalid("json decrypt: invalid parameters");return"string"==typeof e?(r=sjclcrp.misc.cachedPbkdf2(e,o),e=r.key.slice(0,o.ks/32),o.salt=r.salt):sjclcrp.ecc&&e instanceof sjclcrp.ecc.elGamal.secretKey&&(e=e.unkem(sjclcrp.codec.base64.toBits(o.kemtag)).slice(0,o.ks/32)),"string"==typeof l&&(l=sjclcrp.codec.utf8String.toBits(l)),n=new sjclcrp.cipher[o.cipher](e),i=sjclcrp.mode[o.mode].decrypt(n,o.ct,o.iv,l,o.ts),a._add(c,o),c.key=e,1===s.raw?i:sjclcrp.codec.utf8String.fromBits(i)},decrypt:function(e,t,s,c){var i=sjclcrp.json;return i._decrypt(e,i.decode(t),s,c)},encode:function(e){var t,s="{",c="";for(t in e)if(e.hasOwnProperty(t)){if(!t.match(/^[a-z0-9]+$/i))throw new sjclcrp.exception.invalid("json encode: invalid property name");switch(s+=c+'"'+t+'":',c=",",typeof e[t]){case"number":case"boolean":s+=e[t];break;case"string":s+='"'+escape(e[t])+'"';break;case"object":s+='"'+sjclcrp.codec.base64.fromBits(e[t],0)+'"';break;default:throw new sjclcrp.exception.bug("json encode: unsupported type")}}return s+"}"},decode:function(e){if(e=e.replace(/\s/g,""),!e.match(/^\{.*\}$/))throw new sjclcrp.exception.invalid("json decode: this isn't json!");var t,s,c=e.replace(/^\{|\}$/g,"").split(/,/),i={};for(t=0;t<c.length;t++){if(!(s=c[t].match(/^\s*(?:(["']?)([a-z][a-z0-9]*)\1)\s*:\s*(?:(-?\d+)|"([a-z0-9+\/%*_.@=\-]*)"|(true|false))$/i)))throw new sjclcrp.exception.invalid("json decode: this isn't json!");s[3]?i[s[2]]=parseInt(s[3],10):s[4]?i[s[2]]=s[2].match(/^(ct|adata|salt|iv)$/)?sjclcrp.codec.base64.toBits(s[4]):unescape(s[4]):s[5]&&(i[s[2]]="true"===s[5])}return i},_add:function(e,t,s){if(void 0===e&&(e={}),void 0===t)return e;var c;for(c in t)if(t.hasOwnProperty(c)){if(s&&void 0!==e[c]&&e[c]!==t[c])throw new sjclcrp.exception.invalid("required parameter overridden");e[c]=t[c]}return e},_subtract:function(e,t){var s,c={};for(s in e)e.hasOwnProperty(s)&&e[s]!==t[s]&&(c[s]=e[s]);return c},_filter:function(e,t){var s,c={};for(s=0;s<t.length;s++)void 0!==e[t[s]]&&(c[t[s]]=e[t[s]]);return c}},sjclcrp.encrypt=sjclcrp.json.encrypt,sjclcrp.decrypt=sjclcrp.json.decrypt,sjclcrp.misc._pbkdf2Cache={},sjclcrp.misc.cachedPbkdf2=function(e,t){var s,c,i,r,n=sjclcrp.misc._pbkdf2Cache;return t=t||{},r=t.iter||1e3,c=n[e]=n[e]||{},s=c[r]=c[r]||{firstSalt:t.salt&&t.salt.length?t.salt.slice(0):sjclcrp.random.randomWords(2,0)},i=void 0===t.salt?s.firstSalt:t.salt,s[i]=s[i]||sjclcrp.misc.pbkdf2(e,i,t.iter),{key:s[i].slice(0),salt:i.slice(0)}}; sjclcrp.ecc={},sjclcrp.ecc.point=function(e,c,t){void 0===c?this.isIdentity=!0:(c instanceof sjclcrp.bn&&(c=new e.field(c)),t instanceof sjclcrp.bn&&(t=new e.field(t)),this.x=c,this.y=t,this.isIdentity=!1),this.curve=e},sjclcrp.ecc.point.prototype={toJac:function(){return new sjclcrp.ecc.pointJac(this.curve,this.x,this.y,new this.curve.field(1))},mult:function(e){return this.toJac().mult(e,this).toAffine()},mult2:function(e,c,t){return this.toJac().mult2(e,this,c,t).toAffine()},multiples:function(){var e,c,t;if(void 0===this._multiples)for(t=this.toJac().doubl(),e=this._multiples=[new sjclcrp.ecc.point(this.curve),this,t.toAffine()],c=3;16>c;c++)t=t.add(this),e.push(t.toAffine());return this._multiples},negate:function(){var e=new this.curve.field(0).sub(this.y).normalize().reduce();return new sjclcrp.ecc.point(this.curve,this.x,e)},isValid:function(){return this.y.square().equals(this.curve.b.add(this.x.mul(this.curve.a.add(this.x.square()))))},toBits:function(){return sjclcrp.bitArray.concat(this.x.toBits(),this.y.toBits())}},sjclcrp.ecc.pointJac=function(e,c,t,f){void 0===c?this.isIdentity=!0:(this.x=c,this.y=t,this.z=f,this.isIdentity=!1),this.curve=e},sjclcrp.ecc.pointJac.prototype={add:function(e){var c,t,f,i,s,n,r,u,a,l,b,d=this;if(d.curve!==e.curve)throw"sjclcrp.ecc.add(): Points must be on the same curve to add them!";return d.isIdentity?e.toJac():e.isIdentity?d:(c=d.z.square(),t=e.x.mul(c).subM(d.x),t.equals(0)?d.y.equals(e.y.mul(c.mul(d.z)))?d.doubl():new sjclcrp.ecc.pointJac(d.curve):(f=e.y.mul(c.mul(d.z)).subM(d.y),i=t.square(),s=f.square(),n=t.square().mul(t).addM(d.x.add(d.x).mul(i)),r=s.subM(n),u=d.x.mul(i).subM(r).mul(f),a=d.y.mul(t.square().mul(t)),l=u.subM(a),b=d.z.mul(t),new sjclcrp.ecc.pointJac(this.curve,r,l,b)))},doubl:function(){if(this.isIdentity)return this;var e=this.y.square(),c=e.mul(this.x.mul(4)),t=e.square().mul(8),f=this.z.square(),i=this.curve.a.toString()==new sjclcrp.bn(-3).toString()?this.x.sub(f).mul(3).mul(this.x.add(f)):this.x.square().mul(3).add(f.square().mul(this.curve.a)),s=i.square().subM(c).subM(c),n=c.sub(s).mul(i).subM(t),r=this.y.add(this.y).mul(this.z);return new sjclcrp.ecc.pointJac(this.curve,s,n,r)},toAffine:function(){if(this.isIdentity||this.z.equals(0))return new sjclcrp.ecc.point(this.curve);var e=this.z.inverse(),c=e.square();return new sjclcrp.ecc.point(this.curve,this.x.mul(c).fullReduce(),this.y.mul(c.mul(e)).fullReduce())},mult:function(e,c){"number"==typeof e?e=[e]:void 0!==e.limbs&&(e=e.normalize().limbs);var t,f,i=new sjclcrp.ecc.point(this.curve).toJac(),s=c.multiples();for(t=e.length-1;t>=0;t--)for(f=sjclcrp.bn.prototype.radix-4;f>=0;f-=4)i=i.doubl().doubl().doubl().doubl().add(s[e[t]>>f&15]);return i},mult2:function(e,c,t,f){"number"==typeof e?e=[e]:void 0!==e.limbs&&(e=e.normalize().limbs),"number"==typeof t?t=[t]:void 0!==t.limbs&&(t=t.normalize().limbs);var i,s,n,r,u=new sjclcrp.ecc.point(this.curve).toJac(),a=c.multiples(),l=f.multiples();for(i=Math.max(e.length,t.length)-1;i>=0;i--)for(n=0|e[i],r=0|t[i],s=sjclcrp.bn.prototype.radix-4;s>=0;s-=4)u=u.doubl().doubl().doubl().doubl().add(a[n>>s&15]).add(l[r>>s&15]);return u},negate:function(){return this.toAffine().negate().toJac()},isValid:function(){var e=this.z.square(),c=e.square(),t=c.mul(e);return this.y.square().equals(this.curve.b.mul(t).add(this.x.mul(this.curve.a.mul(c).add(this.x.square()))))}},sjclcrp.ecc.curve=function(e,c,t,f,i,s){this.field=e,this.r=new sjclcrp.bn(c),this.a=new e(t),this.b=new e(f),this.G=new sjclcrp.ecc.point(this,new e(i),new e(s))},sjclcrp.ecc.curve.prototype.fromBits=function(e){var c=sjclcrp.bitArray,t=this.field.prototype.exponent+7&-8,f=new sjclcrp.ecc.point(this,this.field.fromBits(c.bitSlice(e,0,t)),this.field.fromBits(c.bitSlice(e,t,2*t)));if(!f.isValid())throw new sjclcrp.exception.corrupt("not on the curve!");return f},sjclcrp.ecc.curves={c192:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p192,"0xffffffffffffffffffffffff99def836146bc9b1b4d22831",-3,"0x64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1","0x188da80eb03090f67cbf20eb43a18800f4ff0afd82ff1012","0x07192b95ffc8da78631011ed6b24cdd573f977a11e794811"),c224:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p224,"0xffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d",-3,"0xb4050a850c04b3abf54132565044b0b7d7bfd8ba270b39432355ffb4","0xb70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21","0xbd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"),c256:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p256,"0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551",-3,"0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b","0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296","0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"),c384:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p384,"0xffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973",-3,"0xb3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef","0xaa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7","0x3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"),k192:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p192k,"0xfffffffffffffffffffffffe26f2fc170f69466a74defd8d",0,3,"0xdb4ff10ec057e9ae26b07d0280b7f4341da5d1b1eae06c7d","0x9b2f2f6d9c5628a7844163d015be86344082aa88d95e2f9d"),k224:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p224k,"0x010000000000000000000000000001dce8d2ec6184caf0a971769fb1f7",0,5,"0xa1455b334df099df30fc28a169a467e9e47075a90f7e650eb6b7a45c","0x7e089fed7fba344282cafbd6f7e319f7c0b0bd59e2ca4bdb556d61a5"),k256:new sjclcrp.ecc.curve(sjclcrp.bn.prime.p256k,"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141",0,7,"0x79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798","0x483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8")},sjclcrp.ecc.basicKey={publicKey:function(e,c){this._curve=e,this._curveBitLength=e.r.bitLength(),this._point=c instanceof Array?e.fromBits(c):c,this.get=function(){var e=this._point.toBits(),c=sjclcrp.bitArray.bitLength(e),t=sjclcrp.bitArray.bitSlice(e,0,c/2),f=sjclcrp.bitArray.bitSlice(e,c/2);return{x:t,y:f}}},secretKey:function(e,c){this._curve=e,this._curveBitLength=e.r.bitLength(),this._exponent=c,this.get=function(){return this._exponent.toBits()}}},sjclcrp.ecc.basicKey.generateKeys=function(e){return function(c,t,f){if(c=c||256,"number"==typeof c&&(c=sjclcrp.ecc.curves["c"+c],void 0===c))throw new sjclcrp.exception.invalid("no such curve");f=f||sjclcrp.bn.random(c.r,t);var i=c.G.mult(f);return{pub:new sjclcrp.ecc[e].publicKey(c,i),sec:new sjclcrp.ecc[e].secretKey(c,f)}}},sjclcrp.ecc.elGamal={generateKeys:sjclcrp.ecc.basicKey.generateKeys("elGamal"),publicKey:function(){sjclcrp.ecc.basicKey.publicKey.apply(this,arguments)},secretKey:function(){sjclcrp.ecc.basicKey.secretKey.apply(this,arguments)}},sjclcrp.ecc.elGamal.publicKey.prototype={kem:function(e){var c=sjclcrp.bn.random(this._curve.r,e),t=this._curve.G.mult(c).toBits(),f=sjclcrp.hash.sha256.hash(this._point.mult(c).toBits());return{key:f,tag:t}}},sjclcrp.ecc.elGamal.secretKey.prototype={unkem:function(e){return sjclcrp.hash.sha256.hash(this._curve.fromBits(e).mult(this._exponent).toBits())},dh:function(e){return sjclcrp.hash.sha256.hash(e._point.mult(this._exponent).toBits())},dhJavaEc:function(e){return e._point.mult(this._exponent).x.toBits()}},sjclcrp.ecc.ecdsa={generateKeys:sjclcrp.ecc.basicKey.generateKeys("ecdsa")},sjclcrp.ecc.ecdsa.publicKey=function(){sjclcrp.ecc.basicKey.publicKey.apply(this,arguments)},sjclcrp.ecc.ecdsa.publicKey.prototype={verify:function(e,c,t){sjclcrp.bitArray.bitLength(e)>this._curveBitLength&&(e=sjclcrp.bitArray.clamp(e,this._curveBitLength));var f=sjclcrp.bitArray,i=this._curve.r,s=this._curveBitLength,n=sjclcrp.bn.fromBits(f.bitSlice(c,0,s)),r=sjclcrp.bn.fromBits(f.bitSlice(c,s,2*s)),u=t?r:r.inverseMod(i),a=sjclcrp.bn.fromBits(e).mul(u).mod(i),l=n.mul(u).mod(i),b=this._curve.G.mult2(a,l,this._point).x;if(n.equals(0)||r.equals(0)||n.greaterEquals(i)||r.greaterEquals(i)||!b.equals(n)){if(void 0===t)return this.verify(e,c,!0);throw new sjclcrp.exception.corrupt("signature didn't check out")}return!0}},sjclcrp.ecc.ecdsa.secretKey=function(){sjclcrp.ecc.basicKey.secretKey.apply(this,arguments)},sjclcrp.ecc.ecdsa.secretKey.prototype={sign:function(e,c,t,f){sjclcrp.bitArray.bitLength(e)>this._curveBitLength&&(e=sjclcrp.bitArray.clamp(e,this._curveBitLength));var i=this._curve.r,s=i.bitLength(),n=f||sjclcrp.bn.random(i.sub(1),c).add(1),r=this._curve.G.mult(n).x.mod(i),u=sjclcrp.bn.fromBits(e).add(r.mul(this._exponent)),a=t?u.inverseMod(i).mul(n).mod(i):u.mul(n.inverseMod(i)).mod(i);return sjclcrp.bitArray.concat(r.toBits(s),a.toBits(s))}}; sjclcrp.mode.gcm={name:"gcm",encrypt:function(c,t,e,r,l){var o,a=t.slice(0),i=sjclcrp.bitArray;return l=l||128,r=r||[],o=sjclcrp.mode.gcm._ctrMode(!0,c,a,r,e,l),i.concat(o.data,o.tag)},decrypt:function(c,t,e,r,l){var o,a,i=t.slice(0),s=sjclcrp.bitArray,g=s.bitLength(i);if(l=l||128,r=r||[],g>=l?(a=s.bitSlice(i,g-l),i=s.bitSlice(i,0,g-l)):(a=i,i=[]),o=sjclcrp.mode.gcm._ctrMode(!1,c,i,r,e,l),!s.equal(o.tag,a))throw new sjclcrp.exception.corrupt("gcm: tag doesn't match");return o.data},_galoisMultiply:function(c,t){var e,r,l,o,a,i,s=sjclcrp.bitArray,g=s._xor4;for(o=[0,0,0,0],a=t.slice(0),e=0;128>e;e++){for(l=0!==(c[Math.floor(e/32)]&1<<31-e%32),l&&(o=g(o,a)),i=0!==(1&a[3]),r=3;r>0;r--)a[r]=a[r]>>>1|(1&a[r-1])<<31;a[0]=a[0]>>>1,i&&(a[0]=a[0]^225<<24)}return o},_ghash:function(c,t,e){var r,l,o=e.length;for(r=t.slice(0),l=0;o>l;l+=4)r[0]^=**********&e[l],r[1]^=**********&e[l+1],r[2]^=**********&e[l+2],r[3]^=**********&e[l+3],r=sjclcrp.mode.gcm._galoisMultiply(r,c);return r},_ctrMode:function(c,t,e,r,l,o){var a,i,s,g,n,h,m,d,j,f,u,_,b=sjclcrp.bitArray;for(j=e.length,f=b.bitLength(e),u=b.bitLength(r),_=b.bitLength(l),a=t.encrypt([0,0,0,0]),96===_?(i=l.slice(0),i=b.concat(i,[1])):(i=sjclcrp.mode.gcm._ghash(a,[0,0,0,0],l),i=sjclcrp.mode.gcm._ghash(a,i,[0,0,Math.floor(_/4294967296),**********&_])),s=sjclcrp.mode.gcm._ghash(a,[0,0,0,0],r),h=i.slice(0),m=s.slice(0),c||(m=sjclcrp.mode.gcm._ghash(a,s,e)),n=0;j>n;n+=4)h[3]++,g=t.encrypt(h),e[n]^=g[0],e[n+1]^=g[1],e[n+2]^=g[2],e[n+3]^=g[3];return e=b.clamp(e,f),c&&(m=sjclcrp.mode.gcm._ghash(a,s,e)),d=[Math.floor(u/4294967296),**********&u,Math.floor(f/4294967296),**********&f],m=sjclcrp.mode.gcm._ghash(a,m,d),g=t.encrypt(i),m[0]^=g[0],m[1]^=g[1],m[2]^=g[2],m[3]^=g[3],{tag:b.bitSlice(m,0,o),data:e}}}; sjclcrp.misc.hmac=function(s,t){this._hash=t=t||sjclcrp.hash.sha256;var e,h=[[],[]],a=t.prototype.blockSize/32;for(this._baseHash=[new t,new t],s.length>a&&(s=t.hash(s)),e=0;a>e;e++)h[0][e]=909522486^s[e],h[1][e]=1549556828^s[e];this._baseHash[0].update(h[0]),this._baseHash[1].update(h[1]),this._resultHash=new t(this._baseHash[0])},sjclcrp.misc.hmac.prototype.encrypt=sjclcrp.misc.hmac.prototype.mac=function(s){if(this._updated)throw new sjclcrp.exception.invalid("encrypt on already updated hmac called!");return this.update(s),this.digest(s)},sjclcrp.misc.hmac.prototype.reset=function(){this._resultHash=new this._hash(this._baseHash[0]),this._updated=!1},sjclcrp.misc.hmac.prototype.update=function(s){this._updated=!0,this._resultHash.update(s)},sjclcrp.misc.hmac.prototype.digest=function(){var s=this._resultHash.finalize(),t=new this._hash(this._baseHash[1]).update(s).finalize();return this.reset(),t}; sjclcrp.mode.ocb2={name:"ocb2",encrypt:function(c,t,e,n,r,o){if(128!==sjclcrp.bitArray.bitLength(e))throw new sjclcrp.exception.invalid("ocb iv must be 128 bits");var i,l,a,s,b=sjclcrp.mode.ocb2._times2,p=sjclcrp.bitArray,m=p._xor4,y=[0,0,0,0],j=b(c.encrypt(e)),h=[];for(n=n||[],r=r||64,i=0;i+4<t.length;i+=4)l=t.slice(i,i+4),y=m(y,l),h=h.concat(m(j,c.encrypt(m(j,l)))),j=b(j);return l=t.slice(i),a=p.bitLength(l),s=c.encrypt(m(j,[0,0,0,a])),l=p.clamp(m(l.concat([0,0,0]),s),a),y=m(y,m(l.concat([0,0,0]),s)),y=c.encrypt(m(y,m(j,b(j)))),n.length&&(y=m(y,o?n:sjclcrp.mode.ocb2.pmac(c,n))),h.concat(p.concat(l,p.clamp(y,r)))},decrypt:function(c,t,e,n,r,o){if(128!==sjclcrp.bitArray.bitLength(e))throw new sjclcrp.exception.invalid("ocb iv must be 128 bits");r=r||64;var i,l,a,s,b=sjclcrp.mode.ocb2._times2,p=sjclcrp.bitArray,m=p._xor4,y=[0,0,0,0],j=b(c.encrypt(e)),h=sjclcrp.bitArray.bitLength(t)-r,u=[];for(n=n||[],i=0;h/32>i+4;i+=4)l=m(j,c.decrypt(m(j,t.slice(i,i+4)))),y=m(y,l),u=u.concat(l),j=b(j);if(a=h-32*i,s=c.encrypt(m(j,[0,0,0,a])),l=m(s,p.clamp(t.slice(i),a).concat([0,0,0])),y=m(y,l),y=c.encrypt(m(y,m(j,b(j)))),n.length&&(y=m(y,o?n:sjclcrp.mode.ocb2.pmac(c,n))),!p.equal(p.clamp(y,r),p.bitSlice(t,h)))throw new sjclcrp.exception.corrupt("ocb: tag doesn't match");return u.concat(p.clamp(l,a))},pmac:function(c,t){var e,n,r=sjclcrp.mode.ocb2._times2,o=sjclcrp.bitArray,i=o._xor4,l=[0,0,0,0],a=c.encrypt([0,0,0,0]);for(a=i(a,r(r(a))),e=0;e+4<t.length;e+=4)a=r(a),l=i(l,c.encrypt(i(a,t.slice(e,e+4))));return n=t.slice(e),o.bitLength(n)<128&&(a=i(a,r(a)),n=o.concat(n,[-2147483648,0,0,0])),l=i(l,n),c.encrypt(i(r(i(a,r(a))),l))},_times2:function(c){return[c[0]<<1^c[1]>>>31,c[1]<<1^c[2]>>>31,c[2]<<1^c[3]>>>31,c[3]<<1^135*(c[0]>>>31)]}}; sjclcrp.misc.pbkdf2=function(t,c,n,i,r){if(n=n||1e3,0>i||0>n)throw sjclcrp.exception.invalid("invalid params to pbkdf2");"string"==typeof t&&(t=sjclcrp.codec.utf8String.toBits(t)),"string"==typeof c&&(c=sjclcrp.codec.utf8String.toBits(c)),r=r||sjclcrp.misc.hmac;var o,e,s,f,l,a=new r(t),p=[],d=sjclcrp.bitArray;for(l=1;32*p.length<(i||1);l++){for(o=e=a.encrypt(d.concat(c,[l])),s=1;n>s;s++)for(e=a.encrypt(e),f=0;f<e.length;f++)o[f]^=e[f];p=p.concat(o)}return i&&(p=d.clamp(p,i)),p}; !function(){function t(t,h,i){return t^h^i}function h(t,h,i){return t&h|~t&i}function i(t,h,i){return(t|~h)^i}function r(t,h,i){return t&i|h&~i}function s(t,h,i){return t^(h|~i)}function n(t,h){return t<<h|t>>>32-h}function e(t){return(255&t)<<24|(65280&t)<<8|(t&255<<16)>>>8|(t&255<<24)>>>24}function f(e){for(var f,c=this._h[0],u=this._h[1],l=this._h[2],j=this._h[3],v=this._h[4],y=this._h[0],d=this._h[1],A=this._h[2],m=this._h[3],z=this._h[4],w=0;16>w;++w)f=n(c+t(u,l,j)+e[a[w]]+_[w],b[w])+v,c=v,v=j,j=n(l,10),l=u,u=f,f=n(y+s(d,A,m)+e[p[w]]+o[w],g[w])+z,y=z,z=m,m=n(A,10),A=d,d=f;for(;32>w;++w)f=n(c+h(u,l,j)+e[a[w]]+_[w],b[w])+v,c=v,v=j,j=n(l,10),l=u,u=f,f=n(y+r(d,A,m)+e[p[w]]+o[w],g[w])+z,y=z,z=m,m=n(A,10),A=d,d=f;for(;48>w;++w)f=n(c+i(u,l,j)+e[a[w]]+_[w],b[w])+v,c=v,v=j,j=n(l,10),l=u,u=f,f=n(y+i(d,A,m)+e[p[w]]+o[w],g[w])+z,y=z,z=m,m=n(A,10),A=d,d=f;for(;64>w;++w)f=n(c+r(u,l,j)+e[a[w]]+_[w],b[w])+v,c=v,v=j,j=n(l,10),l=u,u=f,f=n(y+h(d,A,m)+e[p[w]]+o[w],g[w])+z,y=z,z=m,m=n(A,10),A=d,d=f;for(;80>w;++w)f=n(c+s(u,l,j)+e[a[w]]+_[w],b[w])+v,c=v,v=j,j=n(l,10),l=u,u=f,f=n(y+t(d,A,m)+e[p[w]]+o[w],g[w])+z,y=z,z=m,m=n(A,10),A=d,d=f;f=this._h[1]+l+m,this._h[1]=this._h[2]+j+z,this._h[2]=this._h[3]+v+y,this._h[3]=this._h[4]+c+d,this._h[4]=this._h[0]+u+A,this._h[0]=f}sjclcrp.hash.ripemd160=function(t){t?(this._h=t._h.slice(0),this._buffer=t._buffer.slice(0),this._length=t._length):this.reset()},sjclcrp.hash.ripemd160.hash=function(t){return(new sjclcrp.hash.ripemd160).update(t).finalize()},sjclcrp.hash.ripemd160.prototype={reset:function(){return this._h=c.slice(0),this._buffer=[],this._length=0,this},update:function(t){"string"==typeof t&&(t=sjclcrp.codec.utf8String.toBits(t));var h,i=this._buffer=sjclcrp.bitArray.concat(this._buffer,t),r=this._length,s=this._length=r+sjclcrp.bitArray.bitLength(t);for(h=512+r&-512;s>=h;h+=512){for(var n=i.splice(0,16),c=0;16>c;++c)n[c]=e(n[c]);f.call(this,n)}return this},finalize:function(){var t=sjclcrp.bitArray.concat(this._buffer,[sjclcrp.bitArray.partial(1,1)]),h=(this._length+1)%512,i=(h>448?512:448)-h%448,r=i%32;for(r>0&&(t=sjclcrp.bitArray.concat(t,[sjclcrp.bitArray.partial(r,0)]));i>=32;i-=32)t.push(0);for(t.push(e(0|this._length)),t.push(e(Math.floor(this._length/4294967296)));t.length;){for(var s=t.splice(0,16),n=0;16>n;++n)s[n]=e(s[n]);f.call(this,s)}var c=this._h;this.reset();for(var n=0;5>n;++n)c[n]=e(c[n]);return c}};for(var c=[1732584193,4023233417,2562383102,271733878,3285377520],_=[0,1518500249,1859775393,2400959708,2840853838],o=[1352829926,1548603684,1836072691,2053994217,0],u=4;u>=0;--u)for(var l=1;16>l;++l)_.splice(u,0,_[u]),o.splice(u,0,o[u]);var a=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],p=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],b=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],g=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]}(); sjclcrp.hash.sha1=function(t){t?(this._h=t._h.slice(0),this._buffer=t._buffer.slice(0),this._length=t._length):this.reset()},sjclcrp.hash.sha1.hash=function(t){return(new sjclcrp.hash.sha1).update(t).finalize()},sjclcrp.hash.sha1.prototype={blockSize:512,reset:function(){return this._h=this._init.slice(0),this._buffer=[],this._length=0,this},update:function(t){"string"==typeof t&&(t=sjclcrp.codec.utf8String.toBits(t));var i,h=this._buffer=sjclcrp.bitArray.concat(this._buffer,t),s=this._length,e=this._length=s+sjclcrp.bitArray.bitLength(t);for(i=this.blockSize+s&-this.blockSize;e>=i;i+=this.blockSize)this._block(h.splice(0,16));return this},finalize:function(){var t,i=this._buffer,h=this._h;for(i=sjclcrp.bitArray.concat(i,[sjclcrp.bitArray.partial(1,1)]),t=i.length+2;15&t;t++)i.push(0);for(i.push(Math.floor(this._length/4294967296)),i.push(0|this._length);i.length;)this._block(i.splice(0,16));return this.reset(),h},_init:[1732584193,4023233417,2562383102,271733878,3285377520],_key:[1518500249,1859775393,2400959708,3395469782],_f:function(t,i,h,s){return 19>=t?i&h|~i&s:39>=t?i^h^s:59>=t?i&h|i&s|h&s:79>=t?i^h^s:void 0},_S:function(t,i){return i<<t|i>>>32-t},_block:function(t){var i,h,s,e,r,n,c,l=t.slice(0),f=this._h;for(s=f[0],e=f[1],r=f[2],n=f[3],c=f[4],i=0;79>=i;i++)i>=16&&(l[i]=this._S(1,l[i-3]^l[i-8]^l[i-14]^l[i-16])),h=this._S(5,s)+this._f(i,e,r,n)+c+l[i]+this._key[Math.floor(i/20)]|0,c=n,n=r,r=this._S(30,e),e=s,s=h;f[0]=f[0]+s|0,f[1]=f[1]+e|0,f[2]=f[2]+r|0,f[3]=f[3]+n|0,f[4]=f[4]+c|0}}; sjclcrp.hash.sha256=function(t){this._key[0]||this._precompute(),t?(this._h=t._h.slice(0),this._buffer=t._buffer.slice(0),this._length=t._length):this.reset()},sjclcrp.hash.sha256.hash=function(t){return(new sjclcrp.hash.sha256).update(t).finalize()},sjclcrp.hash.sha256.prototype={blockSize:512,reset:function(){return this._h=this._init.slice(0),this._buffer=[],this._length=0,this},update:function(t){"string"==typeof t&&(t=sjclcrp.codec.utf8String.toBits(t));var h,i=this._buffer=sjclcrp.bitArray.concat(this._buffer,t),s=this._length,e=this._length=s+sjclcrp.bitArray.bitLength(t);for(h=512+s&-512;e>=h;h+=512)this._block(i.splice(0,16));return this},finalize:function(){var t,h=this._buffer,i=this._h;for(h=sjclcrp.bitArray.concat(h,[sjclcrp.bitArray.partial(1,1)]),t=h.length+2;15&t;t++)h.push(0);for(h.push(Math.floor(this._length/4294967296)),h.push(0|this._length);h.length;)this._block(h.splice(0,16));return this.reset(),i},_init:[],_key:[],_precompute:function(){function t(t){return 4294967296*(t-Math.floor(t))|0}var h,i=0,s=2;t:for(;64>i;s++){for(h=2;s>=h*h;h++)if(s%h===0)continue t;8>i&&(this._init[i]=t(Math.pow(s,.5))),this._key[i]=t(Math.pow(s,1/3)),i++}},_block:function(t){var h,i,s,e,r=t.slice(0),n=this._h,c=this._key,o=n[0],f=n[1],l=n[2],_=n[3],a=n[4],u=n[5],p=n[6],b=n[7];for(h=0;64>h;h++)16>h?i=r[h]:(s=r[h+1&15],e=r[h+14&15],i=r[15&h]=(s>>>7^s>>>18^s>>>3^s<<25^s<<14)+(e>>>17^e>>>19^e>>>10^e<<15^e<<13)+r[15&h]+r[h+9&15]|0),i=i+b+(a>>>6^a>>>11^a>>>25^a<<26^a<<21^a<<7)+(p^a&(u^p))+c[h],b=p,p=u,u=a,a=_+i|0,_=l,l=f,f=o,o=i+(f&l^_&(f^l))+(f>>>2^f>>>13^f>>>22^f<<30^f<<19^f<<10)|0;n[0]=n[0]+o|0,n[1]=n[1]+f|0,n[2]=n[2]+l|0,n[3]=n[3]+_|0,n[4]=n[4]+a|0,n[5]=n[5]+u|0,n[6]=n[6]+p|0,n[7]=n[7]+b|0}}; sjclcrp.hash.sha512=function(t){this._key[0]||this._precompute(),t?(this._h=t._h.slice(0),this._buffer=t._buffer.slice(0),this._length=t._length):this.reset()},sjclcrp.hash.sha512.hash=function(t){return(new sjclcrp.hash.sha512).update(t).finalize()},sjclcrp.hash.sha512.prototype={blockSize:1024,reset:function(){return this._h=this._init.slice(0),this._buffer=[],this._length=0,this},update:function(t){"string"==typeof t&&(t=sjclcrp.codec.utf8String.toBits(t));var i,h=this._buffer=sjclcrp.bitArray.concat(this._buffer,t),s=this._length,e=this._length=s+sjclcrp.bitArray.bitLength(t);for(i=1024+s&-1024;e>=i;i+=1024)this._block(h.splice(0,32));return this},finalize:function(){var t,i=this._buffer,h=this._h;for(i=sjclcrp.bitArray.concat(i,[sjclcrp.bitArray.partial(1,1)]),t=i.length+4;31&t;t++)i.push(0);for(i.push(0),i.push(0),i.push(Math.floor(this._length/4294967296)),i.push(0|this._length);i.length;)this._block(i.splice(0,32));return this.reset(),h},_init:[],_initr:[12372232,13281083,9762859,1914609,15106769,4090911,4308331,8266105],_key:[],_keyr:[2666018,15689165,5061423,9034684,4764984,380953,1658779,7176472,197186,7368638,14987916,16757986,8096111,1480369,13046325,6891156,15813330,5187043,9229749,11312229,2818677,10937475,4324308,1135541,6741931,11809296,16458047,15666916,11046850,698149,229999,945776,13774844,2541862,12856045,9810911,11494366,7844520,15576806,8533307,15795044,4337665,16291729,5553712,15684120,6662416,7413802,12308920,13816008,4303699,9366425,10176680,13195875,4295371,6546291,11712675,15708924,1519456,15772530,6568428,6495784,8568297,13007125,7492395,2515356,12632583,14740254,7262584,1535930,13146278,16321966,1853211,294276,13051027,13221564,1051980,4080310,6651434,14088940,4675607],_precompute:function(){function t(t){return 4294967296*(t-Math.floor(t))|0}function i(t){return 1099511627776*(t-Math.floor(t))&255}var h,s=0,e=2;t:for(;80>s;e++){for(h=2;e>=h*h;h++)if(e%h===0)continue t;8>s&&(this._init[2*s]=t(Math.pow(e,.5)),this._init[2*s+1]=i(Math.pow(e,.5))<<24|this._initr[s]),this._key[2*s]=t(Math.pow(e,1/3)),this._key[2*s+1]=i(Math.pow(e,1/3))<<24|this._keyr[s],s++}},_block:function(t){var i,h,s,e=t.slice(0),r=this._h,n=this._key,o=r[0],c=r[1],f=r[2],_=r[3],a=r[4],l=r[5],u=r[6],p=r[7],b=r[8],y=r[9],g=r[10],k=r[11],j=r[12],v=r[13],M=r[14],w=r[15],A=o,d=c,z=f,m=_,S=a,B=l,L=u,q=p,x=b,C=y,D=g,E=k,F=j,G=v,H=M,I=w;for(i=0;80>i;i++){if(16>i)h=e[2*i],s=e[2*i+1];else{var J=e[2*(i-15)],K=e[2*(i-15)+1],N=(K<<31|J>>>1)^(K<<24|J>>>8)^J>>>7,O=(J<<31|K>>>1)^(J<<24|K>>>8)^(J<<25|K>>>7),P=e[2*(i-2)],Q=e[2*(i-2)+1],R=(Q<<13|P>>>19)^(P<<3|Q>>>29)^P>>>6,T=(P<<13|Q>>>19)^(Q<<3|P>>>29)^(P<<26|Q>>>6),U=e[2*(i-7)],V=e[2*(i-7)+1],W=e[2*(i-16)],X=e[2*(i-16)+1];s=O+V,h=N+U+(O>>>0>s>>>0?1:0),s+=T,h+=R+(T>>>0>s>>>0?1:0),s+=X,h+=W+(X>>>0>s>>>0?1:0)}e[2*i]=h|=0,e[2*i+1]=s|=0;var Y=x&D^~x&F,Z=C&E^~C&G,$=A&z^A&S^z&S,ti=d&m^d&B^m&B,ii=(d<<4|A>>>28)^(A<<30|d>>>2)^(A<<25|d>>>7),hi=(A<<4|d>>>28)^(d<<30|A>>>2)^(d<<25|A>>>7),si=(C<<18|x>>>14)^(C<<14|x>>>18)^(x<<23|C>>>9),ei=(x<<18|C>>>14)^(x<<14|C>>>18)^(C<<23|x>>>9),ri=n[2*i],ni=n[2*i+1],oi=I+ei,ci=H+si+(I>>>0>oi>>>0?1:0);oi+=Z,ci+=Y+(Z>>>0>oi>>>0?1:0),oi+=ni,ci+=ri+(ni>>>0>oi>>>0?1:0),oi=oi+s|0,ci+=h+(s>>>0>oi>>>0?1:0);var fi=hi+ti,_i=ii+$+(hi>>>0>fi>>>0?1:0);H=F,I=G,F=D,G=E,D=x,E=C,C=q+oi|0,x=L+ci+(q>>>0>C>>>0?1:0)|0,L=S,q=B,S=z,B=m,z=A,m=d,d=oi+fi|0,A=ci+_i+(oi>>>0>d>>>0?1:0)|0}c=r[1]=c+d|0,r[0]=o+A+(d>>>0>c>>>0?1:0)|0,_=r[3]=_+m|0,r[2]=f+z+(m>>>0>_>>>0?1:0)|0,l=r[5]=l+B|0,r[4]=a+S+(B>>>0>l>>>0?1:0)|0,p=r[7]=p+q|0,r[6]=u+L+(q>>>0>p>>>0?1:0)|0,y=r[9]=y+C|0,r[8]=b+x+(C>>>0>y>>>0?1:0)|0,k=r[11]=k+E|0,r[10]=g+D+(E>>>0>k>>>0?1:0)|0,v=r[13]=v+G|0,r[12]=j+F+(G>>>0>v>>>0?1:0)|0,w=r[15]=w+I|0,r[14]=M+H+(I>>>0>w>>>0?1:0)|0}}; sjclcrp.keyexchange.srp={makeVerifier:function(B,F,D,E){var A;return A=sjclcrp.keyexchange.srp.makeX(B,F,D),A=sjclcrp.bn.fromBits(A),E.g.powermod(A,E.N)},makeX:function(B,F,D){var E=sjclcrp.hash.sha1.hash(B+":"+F);return sjclcrp.hash.sha1.hash(sjclcrp.bitArray.concat(D,E))},knownGroup:function(B){return"string"!=typeof B&&(B=B.toString()),sjclcrp.keyexchange.srp._didInitKnownGroups||sjclcrp.keyexchange.srp._initKnownGroups(),sjclcrp.keyexchange.srp._knownGroups[B]},_didInitKnownGroups:!1,_initKnownGroups:function(){var B,F,D;for(B=0;B<sjclcrp.keyexchange.srp._knownGroupSizes.length;B++)F=sjclcrp.keyexchange.srp._knownGroupSizes[B].toString(),D=sjclcrp.keyexchange.srp._knownGroups[F],D.N=new sjclcrp.bn(D.N),D.g=new sjclcrp.bn(D.g);sjclcrp.keyexchange.srp._didInitKnownGroups=!0},_knownGroupSizes:[1024,1536,2048],_knownGroups:{1024:{N:"EEAF0AB9ADB38DD69C33F80AFA8FC5E86072618775FF3C0B9EA2314C9C256576D674DF7496EA81D3383B4813D692C6E0E0D5D8E250B98BE48E495C1D6089DAD15DC7D7B46154D6B6CE8EF4AD69B15D4982559B297BCF1885C529F566660E57EC68EDBC3C05726CC02FD4CBF4976EAA9AFD5138FE8376435B9FC61D2FC0EB06E3",g:2},1536:{N:"9DEF3CAFB939277AB1F12A8617A47BBBDBA51DF499AC4C80BEEEA9614B19CC4D5F4F5F556E27CBDE51C6A94BE4607A291558903BA0D0F84380B655BB9A22E8DCDF028A7CEC67F0D08134B1C8B97989149B609E0BE3BAB63D47548381DBC5B1FC764E3F4B53DD9DA1158BFD3E2B9C8CF56EDF019539349627DB2FD53D24B7C48665772E437D6C7F8CE442734AF7CCB7AE837C264AE3A9BEB87F8A2FE9B8B5292E5A021FFF5E91479E8CE7A28C2442C6F315180F93499A234DCF76E3FED135F9BB",g:2},2048:{N:"AC6BDB41324A9A9BF166DE5E1389582FAF72B6651987EE07FC3192943DB56050A37329CBB4A099ED8193E0757767A13DD52312AB4B03310DCD7F48A9DA04FD50E8083969EDB767B0CF6095179A163AB3661A05FBD5FAAAE82918A9962F0B93B855F97993EC975EEAA80D740ADBF4FF747359D041D5C33EA71D281E446B14773BCA97B43A23FB801676BD207A436C6481F1D2B9078717461A5B9D32E688F87748544523B524B0D57D5EA77A2775D2ECFA032CFBDBF52FB3786160279004E57AE6AF874E7303CE53299CCC041C7BC308D82A5698F3A8D0C38271AE35F8E9DBFBB694B5C803D89F7AE435DE236D525F54759B65E372FCD68EF20FA7111F9E4AFF73",g:2}}}; sjclcrp.prng=function(e){this._pools=[new sjclcrp.hash.sha256],this._poolEntropy=[0],this._reseedCount=0,this._robins={},this._eventId=0,this._collectorIds={},this._collectorIdNext=0,this._strength=0,this._poolStrength=0,this._nextReseed=0,this._key=[0,0,0,0,0,0,0,0],this._counter=[0,0,0,0],this._cipher=void 0,this._defaultParanoia=e,this._collectorsStarted=!1,this._callbacks={progress:{},seeded:{}},this._callbackI=0,this._NOT_READY=0,this._READY=1,this._REQUIRES_RESEED=2,this._MAX_WORDS_PER_BURST=65536,this._PARANOIA_LEVELS=[0,48,64,96,128,192,256,384,512,768,1024],this._MILLISECONDS_PER_RESEED=3e4,this._BITS_PER_RESEED=80},sjclcrp.prng.prototype={randomWords:function(e,t){var o,n,r=[],i=this.isReady(t);if(i===this._NOT_READY)throw new sjclcrp.exception.notReady("generator isn't seeded");for(i&this._REQUIRES_RESEED&&this._reseedFromPools(!(i&this._READY)),o=0;e>o;o+=4)(o+1)%this._MAX_WORDS_PER_BURST===0&&this._gate(),n=this._gen4words(),r.push(n[0],n[1],n[2],n[3]);return this._gate(),r.slice(0,e)},setDefaultParanoia:function(e,t){if(0===e&&"Setting paranoia=0 will ruin your security; use it only for testing"!==t)throw"Setting paranoia=0 will ruin your security; use it only for testing";this._defaultParanoia=e},addEntropy:function(e,t,o){o=o||"user";var n,r,i,s,h=(new Date).valueOf(),a=this._robins[o],c=this.isReady(),l=0;switch(n=this._collectorIds[o],void 0===n&&(n=this._collectorIds[o]=this._collectorIdNext++),void 0===a&&(a=this._robins[o]=0),this._robins[o]=(this._robins[o]+1)%this._pools.length,typeof e){case"number":void 0===t&&(t=1),this._pools[a].update([n,this._eventId++,1,t,h,1,0|e]);break;case"object":if(s=Object.prototype.toString.call(e),"[object Uint32Array]"===s){for(i=[],r=0;r<e.length;r++)i.push(e[r]);e=i}else for("[object Array]"!==s&&(l=1),r=0;r<e.length&&!l;r++)"number"!=typeof e[r]&&(l=1);if(!l){if(void 0===t)for(t=0,r=0;r<e.length;r++)for(i=e[r];i>0;)t++,i>>>=1;this._pools[a].update([n,this._eventId++,2,t,h,e.length].concat(e))}break;case"string":void 0===t&&(t=e.length),this._pools[a].update([n,this._eventId++,3,t,h,e.length]),this._pools[a].update(e);break;default:l=1}if(l)throw new sjclcrp.exception.bug("random: addEntropy only supports number, array of numbers or string");this._poolEntropy[a]+=t,this._poolStrength+=t,c===this._NOT_READY&&(this.isReady()!==this._NOT_READY&&this._fireEvent("seeded",Math.max(this._strength,this._poolStrength)),this._fireEvent("progress",this.getProgress()))},isReady:function(e){var t=this._PARANOIA_LEVELS[void 0!==e?e:this._defaultParanoia];return this._strength&&this._strength>=t?this._poolEntropy[0]>this._BITS_PER_RESEED&&(new Date).valueOf()>this._nextReseed?this._REQUIRES_RESEED|this._READY:this._READY:this._poolStrength>=t?this._REQUIRES_RESEED|this._NOT_READY:this._NOT_READY},getProgress:function(e){var t=this._PARANOIA_LEVELS[e?e:this._defaultParanoia];return this._strength>=t?1:this._poolStrength>t?1:this._poolStrength/t},startCollectors:function(){if(!this._collectorsStarted){if(this._eventListener={loadTimeCollector:this._bind(this._loadTimeCollector),mouseCollector:this._bind(this._mouseCollector),keyboardCollector:this._bind(this._keyboardCollector),accelerometerCollector:this._bind(this._accelerometerCollector),touchCollector:this._bind(this._touchCollector)},window.addEventListener)window.addEventListener("load",this._eventListener.loadTimeCollector,!1),window.addEventListener("mousemove",this._eventListener.mouseCollector,!1),window.addEventListener("keypress",this._eventListener.keyboardCollector,!1),window.addEventListener("devicemotion",this._eventListener.accelerometerCollector,!1),window.addEventListener("touchmove",this._eventListener.touchCollector,!1);else{if(!document.attachEvent)throw new sjclcrp.exception.bug("can't attach event");document.attachEvent("onload",this._eventListener.loadTimeCollector),document.attachEvent("onmousemove",this._eventListener.mouseCollector),document.attachEvent("keypress",this._eventListener.keyboardCollector)}this._collectorsStarted=!0}},stopCollectors:function(){this._collectorsStarted&&(window.removeEventListener?(window.removeEventListener("load",this._eventListener.loadTimeCollector,!1),window.removeEventListener("mousemove",this._eventListener.mouseCollector,!1),window.removeEventListener("keypress",this._eventListener.keyboardCollector,!1),window.removeEventListener("devicemotion",this._eventListener.accelerometerCollector,!1),window.removeEventListener("touchmove",this._eventListener.touchCollector,!1)):document.detachEvent&&(document.detachEvent("onload",this._eventListener.loadTimeCollector),document.detachEvent("onmousemove",this._eventListener.mouseCollector),document.detachEvent("keypress",this._eventListener.keyboardCollector)),this._collectorsStarted=!1)},addEventListener:function(e,t){this._callbacks[e][this._callbackI++]=t},removeEventListener:function(e,t){var o,n,r=this._callbacks[e],i=[];for(n in r)r.hasOwnProperty(n)&&r[n]===t&&i.push(n);for(o=0;o<i.length;o++)n=i[o],delete r[n]},_bind:function(e){var t=this;return function(){e.apply(t,arguments)}},_gen4words:function(){for(var e=0;4>e&&(this._counter[e]=this._counter[e]+1|0,!this._counter[e]);e++);return this._cipher.encrypt(this._counter)},_gate:function(){this._key=this._gen4words().concat(this._gen4words()),this._cipher=new sjclcrp.cipher.aes(this._key)},_reseed:function(e){this._key=sjclcrp.hash.sha256.hash(this._key.concat(e)),this._cipher=new sjclcrp.cipher.aes(this._key);for(var t=0;4>t&&(this._counter[t]=this._counter[t]+1|0,!this._counter[t]);t++);},_reseedFromPools:function(e){var t,o=[],n=0;for(this._nextReseed=o[0]=(new Date).valueOf()+this._MILLISECONDS_PER_RESEED,t=0;16>t;t++)o.push(4294967296*Math.random()|0);for(t=0;t<this._pools.length&&(o=o.concat(this._pools[t].finalize()),n+=this._poolEntropy[t],this._poolEntropy[t]=0,e||!(this._reseedCount&1<<t));t++);this._reseedCount>=1<<this._pools.length&&(this._pools.push(new sjclcrp.hash.sha256),this._poolEntropy.push(0)),this._poolStrength-=n,n>this._strength&&(this._strength=n),this._reseedCount++,this._reseed(o)},_keyboardCollector:function(){this._addCurrentTimeToEntropy(1)},_mouseCollector:function(e){var t,o;try{t=e.x||e.clientX||e.offsetX||0,o=e.y||e.clientY||e.offsetY||0}catch(n){t=0,o=0}0!=t&&0!=o&&sjclcrp.random.addEntropy([t,o],2,"mouse"),this._addCurrentTimeToEntropy(0)},_touchCollector:function(e){var t=e.touches[0]||e.changedTouches[0],o=t.pageX||t.clientX,n=t.pageY||t.clientY;sjclcrp.random.addEntropy([o,n],1,"touch"),this._addCurrentTimeToEntropy(0)},_loadTimeCollector:function(){this._addCurrentTimeToEntropy(2)},_addCurrentTimeToEntropy:function(e){"undefined"!=typeof window&&window.performance&&"function"==typeof window.performance.now?sjclcrp.random.addEntropy(window.performance.now(),e,"loadtime"):sjclcrp.random.addEntropy((new Date).valueOf(),e,"loadtime")},_accelerometerCollector:function(e){var t=e.accelerationIncludingGravity.x||e.accelerationIncludingGravity.y||e.accelerationIncludingGravity.z;if(window.orientation){var o=window.orientation;"number"==typeof o&&sjclcrp.random.addEntropy(o,1,"accelerometer")}t&&sjclcrp.random.addEntropy(t,2,"accelerometer"),this._addCurrentTimeToEntropy(0)},_fireEvent:function(e,t){var o,n=sjclcrp.random._callbacks[e],r=[];for(o in n)n.hasOwnProperty(o)&&r.push(n[o]);for(o=0;o<r.length;o++)r[o](t)}},sjclcrp.random=new sjclcrp.prng(6),function(){function e(){try{return require("crypto")}catch(e){return null}}try{var t,o,n;if("undefined"!=typeof module&&module.exports&&(o=e())&&o.randomBytes)t=o.randomBytes(128),t=new Uint32Array(new Uint8Array(t).buffer),sjclcrp.random.addEntropy(t,1024,"crypto.randomBytes");else if("undefined"!=typeof window&&"undefined"!=typeof Uint32Array){if(n=new Uint32Array(32),window.crypto&&window.crypto.getRandomValues)window.crypto.getRandomValues(n);else{if(!window.msCrypto||!window.msCrypto.getRandomValues)return;window.msCrypto.getRandomValues(n)}sjclcrp.random.addEntropy(n,1024,"crypto.getRandomValues")}}catch(r){"undefined"!=typeof window&&window.console&&(console.log("There was an error collecting entropy from the browser:"),console.log(r))}}(); var dbits,canary=0xdeadbeefcafe,j_lm=15715070==(canary&16777215);function BigInteger(a,b,c){null!=a&&("number"==typeof a?this.fromNumber(a,b,c):null==b&&"string"!=typeof a?this.fromString(a,256):this.fromString(a,b))}function nbi(){return new BigInteger(null)}function am1(a,b,c,d,e,f){for(;0<=--f;){var g=b*this[a++]+c[d]+e;e=Math.floor(g/67108864);c[d++]=g&67108863}return e} function am2(a,b,c,d,e,f){var g=b&32767;for(b>>=15;0<=--f;){var h=this[a]&32767,k=this[a++]>>15,m=b*h+k*g,h=g*h+((m&32767)<<15)+c[d]+(e&1073741823);e=(h>>>30)+(m>>>15)+b*k+(e>>>30);c[d++]=h&1073741823}return e}function am3(a,b,c,d,e,f){var g=b&16383;for(b>>=14;0<=--f;){var h=this[a]&16383,k=this[a++]>>14,m=b*h+k*g,h=g*h+((m&16383)<<14)+c[d]+e;e=(h>>28)+(m>>14)+b*k;c[d++]=h&268435455}return e} j_lm&&"Microsoft Internet Explorer"==navigator.appName?(BigInteger.prototype.am=am2,dbits=30):j_lm&&"Netscape"!=navigator.appName?(BigInteger.prototype.am=am1,dbits=26):(BigInteger.prototype.am=am3,dbits=28);BigInteger.prototype.DB=dbits;BigInteger.prototype.DM=(1<<dbits)-1;BigInteger.prototype.DV=1<<dbits;var BI_FP=52;BigInteger.prototype.FV=Math.pow(2,BI_FP);BigInteger.prototype.F1=BI_FP-dbits;BigInteger.prototype.F2=2*dbits-BI_FP;var BI_RM="0123456789abcdefghijklmnopqrstuvwxyz",BI_RC=[],rr,vv; rr=48;for(vv=0;9>=vv;++vv)BI_RC[rr++]=vv;rr=97;for(vv=10;36>vv;++vv)BI_RC[rr++]=vv;rr=65;for(vv=10;36>vv;++vv)BI_RC[rr++]=vv;function int2char(a){return BI_RM.charAt(a)}function intAt(a,b){var c=BI_RC[a.charCodeAt(b)];return null==c?-1:c}function bnpCopyTo(a){for(var b=this.t-1;0<=b;--b)a[b]=this[b];a.t=this.t;a.s=this.s}function bnpFromInt(a){this.t=1;this.s=0>a?-1:0;0<a?this[0]=a:-1>a?this[0]=a+this.DV:this.t=0}function nbv(a){var b=nbi();b.fromInt(a);return b} function bnpFromString(a,b){var c;if(16==b)c=4;else if(8==b)c=3;else if(256==b)c=8;else if(2==b)c=1;else if(32==b)c=5;else if(4==b)c=2;else{this.fromRadix(a,b);return}this.s=this.t=0;for(var d=a.length,e=!1,f=0;0<=--d;){var g=8==c?a[d]&255:intAt(a,d);0>g?"-"==a.charAt(d)&&(e=!0):(e=!1,0==f?this[this.t++]=g:f+c>this.DB?(this[this.t-1]|=(g&(1<<this.DB-f)-1)<<f,this[this.t++]=g>>this.DB-f):this[this.t-1]|=g<<f,f+=c,f>=this.DB&&(f-=this.DB))}8==c&&0!=(a[0]&128)&&(this.s=-1,0<f&&(this[this.t-1]|=(1<<this.DB- f)-1<<f));this.clamp();e&&BigInteger.ZERO.subTo(this,this)}function bnpClamp(){for(var a=this.s&this.DM;0<this.t&&this[this.t-1]==a;)--this.t} function bnToString(a){if(0>this.s)return"-"+this.negate().toString(a);if(16==a)a=4;else if(8==a)a=3;else if(2==a)a=1;else if(32==a)a=5;else if(4==a)a=2;else return this.toRadix(a);var b=(1<<a)-1,c,d=!1,e="",f=this.t,g=this.DB-f*this.DB%a;if(0<f--)for(g<this.DB&&0<(c=this[f]>>g)&&(d=!0,e=int2char(c));0<=f;)g<a?(c=(this[f]&(1<<g)-1)<<a-g,c|=this[--f]>>(g+=this.DB-a)):(c=this[f]>>(g-=a)&b,0>=g&&(g+=this.DB,--f)),0<c&&(d=!0),d&&(e+=int2char(c));return d?e:"0"} function bnNegate(){var a=nbi();BigInteger.ZERO.subTo(this,a);return a}function bnAbs(){return 0>this.s?this.negate():this}function bnCompareTo(a){var b=this.s-a.s;if(0!=b)return b;var c=this.t,b=c-a.t;if(0!=b)return 0>this.s?-b:b;for(;0<=--c;)if(0!=(b=this[c]-a[c]))return b;return 0}function nbits(a){var b=1,c;0!=(c=a>>>16)&&(a=c,b+=16);0!=(c=a>>8)&&(a=c,b+=8);0!=(c=a>>4)&&(a=c,b+=4);0!=(c=a>>2)&&(a=c,b+=2);0!=a>>1&&(b+=1);return b} function bnBitLength(){return 0>=this.t?0:this.DB*(this.t-1)+nbits(this[this.t-1]^this.s&this.DM)}function bnpDLShiftTo(a,b){var c;for(c=this.t-1;0<=c;--c)b[c+a]=this[c];for(c=a-1;0<=c;--c)b[c]=0;b.t=this.t+a;b.s=this.s}function bnpDRShiftTo(a,b){for(var c=a;c<this.t;++c)b[c-a]=this[c];b.t=Math.max(this.t-a,0);b.s=this.s} function bnpLShiftTo(a,b){var c=a%this.DB,d=this.DB-c,e=(1<<d)-1,f=Math.floor(a/this.DB),g=this.s<<c&this.DM,h;for(h=this.t-1;0<=h;--h)b[h+f+1]=this[h]>>d|g,g=(this[h]&e)<<c;for(h=f-1;0<=h;--h)b[h]=0;b[f]=g;b.t=this.t+f+1;b.s=this.s;b.clamp()} function bnpRShiftTo(a,b){b.s=this.s;var c=Math.floor(a/this.DB);if(c>=this.t)b.t=0;else{var d=a%this.DB,e=this.DB-d,f=(1<<d)-1;b[0]=this[c]>>d;for(var g=c+1;g<this.t;++g)b[g-c-1]|=(this[g]&f)<<e,b[g-c]=this[g]>>d;0<d&&(b[this.t-c-1]|=(this.s&f)<<e);b.t=this.t-c;b.clamp()}} function bnpSubTo(a,b){for(var c=0,d=0,e=Math.min(a.t,this.t);c<e;)d+=this[c]-a[c],b[c++]=d&this.DM,d>>=this.DB;if(a.t<this.t){for(d-=a.s;c<this.t;)d+=this[c],b[c++]=d&this.DM,d>>=this.DB;d+=this.s}else{for(d+=this.s;c<a.t;)d-=a[c],b[c++]=d&this.DM,d>>=this.DB;d-=a.s}b.s=0>d?-1:0;-1>d?b[c++]=this.DV+d:0<d&&(b[c++]=d);b.t=c;b.clamp()} function bnpMultiplyTo(a,b){var c=this.abs(),d=a.abs(),e=c.t;for(b.t=e+d.t;0<=--e;)b[e]=0;for(e=0;e<d.t;++e)b[e+c.t]=c.am(0,d[e],b,e,0,c.t);b.s=0;b.clamp();this.s!=a.s&&BigInteger.ZERO.subTo(b,b)}function bnpSquareTo(a){for(var b=this.abs(),c=a.t=2*b.t;0<=--c;)a[c]=0;for(c=0;c<b.t-1;++c){var d=b.am(c,b[c],a,2*c,0,1);(a[c+b.t]+=b.am(c+1,2*b[c],a,2*c+1,d,b.t-c-1))>=b.DV&&(a[c+b.t]-=b.DV,a[c+b.t+1]=1)}0<a.t&&(a[a.t-1]+=b.am(c,b[c],a,2*c,0,1));a.s=0;a.clamp()} function bnpDivRemTo(a,b,c){var d=a.abs();if(!(0>=d.t)){var e=this.abs();if(e.t<d.t)null!=b&&b.fromInt(0),null!=c&&this.copyTo(c);else{null==c&&(c=nbi());var f=nbi(),g=this.s;a=a.s;var h=this.DB-nbits(d[d.t-1]);0<h?(d.lShiftTo(h,f),e.lShiftTo(h,c)):(d.copyTo(f),e.copyTo(c));d=f.t;e=f[d-1];if(0!=e){var k=e*(1<<this.F1)+(1<d?f[d-2]>>this.F2:0),m=this.FV/k,k=(1<<this.F1)/k,r=1<<this.F2,n=c.t,p=n-d,l=null==b?nbi():b;f.dlShiftTo(p,l);0<=c.compareTo(l)&&(c[c.t++]=1,c.subTo(l,c));BigInteger.ONE.dlShiftTo(d, l);for(l.subTo(f,f);f.t<d;)f[f.t++]=0;for(;0<=--p;){var q=c[--n]==e?this.DM:Math.floor(c[n]*m+(c[n-1]+r)*k);if((c[n]+=f.am(0,q,c,p,0,d))<q)for(f.dlShiftTo(p,l),c.subTo(l,c);c[n]<--q;)c.subTo(l,c)}null!=b&&(c.drShiftTo(d,b),g!=a&&BigInteger.ZERO.subTo(b,b));c.t=d;c.clamp();0<h&&c.rShiftTo(h,c);0>g&&BigInteger.ZERO.subTo(c,c)}}}}function bnMod(a){var b=nbi();this.abs().divRemTo(a,null,b);0>this.s&&0<b.compareTo(BigInteger.ZERO)&&a.subTo(b,b);return b}function Classic(a){this.m=a} function cConvert(a){return 0>a.s||0<=a.compareTo(this.m)?a.mod(this.m):a}function cRevert(a){return a}function cReduce(a){a.divRemTo(this.m,null,a)}function cMulTo(a,b,c){a.multiplyTo(b,c);this.reduce(c)}function cSqrTo(a,b){a.squareTo(b);this.reduce(b)}Classic.prototype.convert=cConvert;Classic.prototype.revert=cRevert;Classic.prototype.reduce=cReduce;Classic.prototype.mulTo=cMulTo;Classic.prototype.sqrTo=cSqrTo; function bnpInvDigit(){if(1>this.t)return 0;var a=this[0];if(0==(a&1))return 0;var b=a&3,b=b*(2-(a&15)*b)&15,b=b*(2-(a&255)*b)&255,b=b*(2-((a&65535)*b&65535))&65535,b=b*(2-a*b%this.DV)%this.DV;return 0<b?this.DV-b:-b}function Montgomery(a){this.m=a;this.mp=a.invDigit();this.mpl=this.mp&32767;this.mph=this.mp>>15;this.um=(1<<a.DB-15)-1;this.mt2=2*a.t} function montConvert(a){var b=nbi();a.abs().dlShiftTo(this.m.t,b);b.divRemTo(this.m,null,b);0>a.s&&0<b.compareTo(BigInteger.ZERO)&&this.m.subTo(b,b);return b}function montRevert(a){var b=nbi();a.copyTo(b);this.reduce(b);return b} function montReduce(a){for(;a.t<=this.mt2;)a[a.t++]=0;for(var b=0;b<this.m.t;++b){var c=a[b]&32767,d=c*this.mpl+((c*this.mph+(a[b]>>15)*this.mpl&this.um)<<15)&a.DM,c=b+this.m.t;for(a[c]+=this.m.am(0,d,a,b,0,this.m.t);a[c]>=a.DV;)a[c]-=a.DV,a[++c]++}a.clamp();a.drShiftTo(this.m.t,a);0<=a.compareTo(this.m)&&a.subTo(this.m,a)}function montSqrTo(a,b){a.squareTo(b);this.reduce(b)}function montMulTo(a,b,c){a.multiplyTo(b,c);this.reduce(c)}Montgomery.prototype.convert=montConvert; Montgomery.prototype.revert=montRevert;Montgomery.prototype.reduce=montReduce;Montgomery.prototype.mulTo=montMulTo;Montgomery.prototype.sqrTo=montSqrTo;function bnpIsEven(){return 0==(0<this.t?this[0]&1:this.s)}function bnpExp(a,b){if(**********<a||1>a)return BigInteger.ONE;var c=nbi(),d=nbi(),e=b.convert(this),f=nbits(a)-1;for(e.copyTo(c);0<=--f;)if(b.sqrTo(c,d),0<(a&1<<f))b.mulTo(d,e,c);else var g=c,c=d,d=g;return b.revert(c)} function bnModPowInt(a,b){var c;c=256>a||b.isEven()?new Classic(b):new Montgomery(b);return this.exp(a,c)}BigInteger.prototype.copyTo=bnpCopyTo;BigInteger.prototype.fromInt=bnpFromInt;BigInteger.prototype.fromString=bnpFromString;BigInteger.prototype.clamp=bnpClamp;BigInteger.prototype.dlShiftTo=bnpDLShiftTo;BigInteger.prototype.drShiftTo=bnpDRShiftTo;BigInteger.prototype.lShiftTo=bnpLShiftTo;BigInteger.prototype.rShiftTo=bnpRShiftTo;BigInteger.prototype.subTo=bnpSubTo; BigInteger.prototype.multiplyTo=bnpMultiplyTo;BigInteger.prototype.squareTo=bnpSquareTo;BigInteger.prototype.divRemTo=bnpDivRemTo;BigInteger.prototype.invDigit=bnpInvDigit;BigInteger.prototype.isEven=bnpIsEven;BigInteger.prototype.exp=bnpExp;BigInteger.prototype.toString=bnToString;BigInteger.prototype.negate=bnNegate;BigInteger.prototype.abs=bnAbs;BigInteger.prototype.compareTo=bnCompareTo;BigInteger.prototype.bitLength=bnBitLength;BigInteger.prototype.mod=bnMod;BigInteger.prototype.modPowInt=bnModPowInt; BigInteger.ZERO=nbv(0);BigInteger.ONE=nbv(1);function parseBigInt(a,b){return new BigInteger(a,b)}function linebrk(a,b){for(var c="",d=0;d+b<a.length;)c+=a.substring(d,d+b)+"\n",d+=b;return c+a.substring(d,a.length)}function byte2Hex(a){return 16>a?"0"+a.toString(16):a.toString(16)} function pkcs1pad2(a,b){if(b<a.length+11)return alert("Message too long for RSA"),null;for(var c=[],d=a.length-1;0<=d&&0<b;){var e=a.charCodeAt(d--);128>e?c[--b]=e:127<e&&2048>e?(c[--b]=e&63|128,c[--b]=e>>6|192):(c[--b]=e&63|128,c[--b]=e>>6&63|128,c[--b]=e>>12|224)}c[--b]=0;d=new SecureRandom;for(e=[];2<b;){for(e[0]=0;0==e[0];)d.nextBytes(e);c[--b]=e[0]}c[--b]=2;c[--b]=0;return new BigInteger(c)}function RSAKey(){this.n=null;this.e=0;this.coeff=this.dmq1=this.dmp1=this.q=this.p=this.d=null} function RSASetPublic(a,b){null!=a&&null!=b&&0<a.length&&0<b.length?(this.n=parseBigInt(a,16),this.e=parseInt(b,16)):alert("Invalid RSA public key")}function RSADoPublic(a){return a.modPowInt(this.e,this.n)}function RSAEncrypt(a){a=pkcs1pad2(a,this.n.bitLength()+7>>3);if(null==a)return null;a=this.doPublic(a);if(null==a)return null;a=a.toString(16);return 0==(a.length&1)?a:"0"+a}RSAKey.prototype.doPublic=RSADoPublic;RSAKey.prototype.setPublic=RSASetPublic;RSAKey.prototype.encrypt=RSAEncrypt; var c0=[140,238,115,203,224,204,230,95,1,120,172,168,212,97,247,191,200,233,63,243,232,202,243,12,22,115,181,189,157,97,189,188,197,243,115,205,224,211,252,95,17,100,175,157,218,116,171,187,156,255,60,218,160,199,228,30,7,33,184,225,140,106,229,246,216,160,32,149,229,129,162,83,30,60,189,150,217,27,175,189,141,252,75,136,212,135,244,16,7,41,184,240,217,125,176,232,142,249,43,221,162,151,187,23,72,50,238,241,139,29,231,135,143,249,59,136,181,222,201,27,40,44,239,255,214,110,235,166,215,161,44,219,210,216,207,82,70,51,241,169,196,119,165,232,129,169,39,141,177,143,164,75,77,63,226,243,141,111,189,228,142,173,42,145,225,192,245,65,75,63,184,230,216,107,171,184,235,249,77,148,186,142,187,89,71,48,232,250,221,126,176,236,132,165,46,135,183,216,187,65,75,63,236,247,193,35,190,184,235,249,77,144,183,216,185,78,74,41,180,177,130,122,191,191,157,181,116,146,184,149,180,77,68,53,235,249,209,117,181,238,136,163,46,135,237,149,172,65,75,49,230,229,129,58,232,228,142,163,116,146,184,145,247,89,71,48,232,250,221,126,176,236,132,165,46,135,183,216,187,65,75,63,236,246,143,41,241,242,214,160,32,130,239,128,241,82,70,51,231,171,194,109,175,190,155,182,57,213,180,143,160,67,23,90,184,144,214,32,168,235,140,255,75,221,212,145,161,77,74,41,176,177,142,122,191,184,235,249,77,148,186,142,191,25,88,48,250,255,216,114,180,238,136,174,38,141,177,130,172,65,19,40,226,243,215,118,185,242,220,225,119,135,183,130,244,84,68,44,244,175,178,34,222,247,131,175,57,159,187,141,166,72,65,57,239,251,221,126,189,228,142,251,57,135,183,130,162,69,93,100,225,175,178,34,222,243,142,251,59,136,182,148,254,3,30,61,224,168,196,110,229,241,129,180,54,139,184,136,165,75,77,50,234,249,209,120,189,228,214,180,46,135,183,140,168,87,25,125,183,243,215,120,229,241,129,176,117,159,187,141,166,72,65,57,239,251,221,126,189,228,142,251,57,135,183,130,162,68,20,90,236,144,212,46,184,187,235,172,77,132,229,135,224,26,1,116,174,163,201,39,254,215,186,251,101,215,234,200,251,16,27,33,184,168,154,15,211,242,209,180,107,203,236,200,231,13,27,33,184,168,154,22,176,232,152,252,60,226,188,132,190,74,69,45,232,255,197,117,183,246,130,171,60,136,177,144,163,79,89,51,240,251,217,106,182,232,156,169,36,149,186,138,190,77,77,45,238,253,197,119,177,246,132,177,38,139,165,137,166,83,65,55,240,254,209,106,176,234,156,175,34,149,184,136,190,73,89,55,232,225,220,112,175,238,136,177,36,137,165,143,160,83,71,53,240,252,223,106,187,246,133,170,60,141,176,144,166,78,89,50,239,225,219,115,175,235,135,177,41,149,184,144,167,70,89,52,237,225,221,117,175,233,133,177,34,142,165,141,171,83,68,48,240,254,197,112,178,246,133,174,60,141,188,144,161,72,89,51,229,225,219,119,175,235,131,177,37,149,191,143,190,74,64,45,232,250,197,117,186,246,131,172,60,139,186,144,163,74,89,54,129,225,223,114,170,167,214,232,126,218,253,213,253,17,85,101,185,190,160,40,245,191,194,238,117,240,217,148,243,86,14,115,185,185,156,52,237,250,212,248,99,233,186,142,186,30,89,90,232,253,197,126,175,238,136,177,33,143,165,137,164,83,71,53,240,251,221,106,176,232,156,174,41,149,190,144,166,72,89,48,233,225,220,115,175,232,131,177,38,138,165,143,163,83,70,57,240,251,197,114,181,246,129,169,60,140,189,144,160,77,89,55,238,225,218,118,175,233,135,177,37,149,189,137,190,78,70,45,233,254,197,116,178,246,134,172,60,139,176,144,161,73,89,53,240,249,221,106,178,232,156,168,34,149,187,140,190,73,69,45,238,245,197,117,182,246,131,177,36,138,165,141,163,83,64,48,240,252,208,106,182,227,156,175,39,149,186,136,190,77,89,53,238,225,216,118,175,239,128,177,33,129,165,137,170,83,71,55,240,254,218,106,178,246,132,172,60,128,165,136,171,83,68,54,240,248,222,106,177,239,237,177,38,141,160,193,159,117,19,116,178,174,157,47,236,180,144,249,117,202,217,255,163,87,20,40,167,169,140,53,211,233,130,181,113,149,210,137,165,83,65,56,240,249,216,106,176,233,156,175,37,149,184,139,190,70,89,48,240,248,209,106,182,234,156,169,34,149,186,136,190,77,67,45,237,245,197,119,179,246,130,177,37,128,165,137,163,83,65,50,240,254,220,106,177,237,156,172,41,149,184,141,190,76,89,55,236,225,220,116,175,238,132,177,35,143,165,138,161,83,64,52,240,249,222,106,176,227,156,174,33,149,187,143,190,78,64,45,235,225,223,116,175,239,132,177,36,143,165,143,170,83,70,49,240,255,219,106,178,238,156,171,60,143,184,144,167,76,89,53,233,225,218,113,175,232,137,177,34,136,165,141,161,83,64,45,238,245,197,116,179,246,129,175,60,141,212,144,167,73,92,58,189,150,216,27,190,242,209,198,32,228,181,128,160,75,83,51,233,252,223,115,187,232,132,173,108,216,210,141,207,65,75,63,228,228,215,120,189,234,139,252,75,137,212,130,172,65,72,53,231,191,140,50,246,168,222,189,113,196,239,201,252,28,1,104,179,163,201,34,230,169,252,248,118,205,218,212,251,25,1,41,189,225,139,111,248,187,235,173,77,132,161,221,201,79,40,61,224,175,207,116,181,226,132,174,37,141,188,137,238,30,46,49,129,243,215,120,177,226,157,255,57,135,183,130,162,68,20,90,237,144,212,110,226,129,129,192,44,133,235,154,160,73,77,53,239,248,221,115,182,166,209,198,33,228,183,130,172,77,77,44,190,228,215,120,189,234,139,239,117,205,252,206,252,95,20,124,209,199,143,51,237,185,196,244,127,215,169,216,247,12,37,66,238,229,136,111,248,187,235,173,77,132,161,221,201,79,40,61,224,249,149,39,216,235,237,163,46,135,187,136,180,78,64,40,226,243,215,118,184,187,235,172,77,132,232,231,163,34,73,61,228,243,215,120,179,225,194,248,100,204,251,210,178,27,16,114,140,254,219,110,226,246,235,172,36,149,184,139,190,78,68,45,238,249,197,119,175,239,156,174,60,139,177,144,163,74,89,55,240,255,216,106,178,234,156,175,35,149,184,133,190,78,71,45,232,225,219,112,175,226,156,172,38,149,190,144,160,72,89,51,236,225,216,117,175,232,156,169,33,149,188,142,190,76,68,45,239,250,197,114,180,246,133,168,60,138,185,144,166,79,89,52,237,225,221,115,175,233,131,177,36,129,165,136,166,83,65,56,240,254,208,106,182,236,156,174,36,149,188,143,190,75,67,45,232,255,197,115,179,246,131,171,60,139,176,144,161,77,40,45,232,245,192,59,142,208,214,232,126,218,253,213,253,17,85,101,185,190,175,110,226,246,210,180,107,207,232,206,178,28,72,90,236,225,217,27,175,190,141,198,32,149,185,225,169,28,46,49,129,240,136,125,224,129,129,192,45,137,178,216,247,12,37,50,238,229,138,106,216,233,130,177,33,149,187,144,161,83,65,45,233,225,221,106,182,246,134,177,39,149,177,144,171,83,77,45,229,225,216,118,175,235,129,177,33,139,165,141,161,83,68,51,240,252,218,106,178,238,156,172,37,149,184,138,190,78,66,45,237,251,197,119,180,246,129,165,60,136,176,144,160,79,89,51,237,225,219,118,175,232,129,177,34,139,165,142,161,83,71,53,240,255,220,106,177,238,156,175,37,149,187,138,190,77,66,45,238,245,197,116,186,246,130,165,60,139,176,144,161,79,89,50,237,225,218,116,175,235,237,177,36,129,160,135,228,30,7,33,185,240,138,29,179,135,238,255,75,137,212,135,228,30,7,33,186,240,138,29,178,135,238,255,75,136,212,135,241,66,46,90,237,249,197,114,175,235,131,177,33,149,187,144,163,74,89,48,237,225,209,106,176,246,129,173,60,143,165,141,160,83,64,45,229,225,217,106,180,135,156,198,32,149,184,137,190,72,89,53,240,252,221,106,177,246,129,174,60,136,165,141,162,83,67,45,237,255,197,119,178,246,137,177,37,149,186,144,170,34,89,90,232,225,216,106,178,238,156,165,60,136,186,144,164,83,71,45,237,252,197,119,182,246,129,175,60,128,165,139,190,76,89,48,236,225,220,106,179,135,156,198,33,140,165,141,160,83,77,45,238,225,221,106,186,246,129,177,39,149,188,144,163,78,89,50,240,252,221,106,178,234,156,173,60,143,165,141,161,34,40,90,185,243,215,120,176,234,150,175,108,220,183,130,172,77,67,39,237,144,178,35,189,228,142,175,39,159,184,137,207,67,73,51,228,243,215,120,179,225,211,225,45,226,210,141,167,83,68,45,228,225,216,114,175,236,156,172,33,149,186,144,166,83,76,45,235,225,219,106,178,233,156,172,34,149,185,144,167,83,68,49,129,225,178,117,175,235,131,177,36,149,190,144,163,74,89,51,240,245,197,119,183,246,129,175,60,137,165,177,152,78,89,48,236,225,223,106,186,246,129,172,60,140,212,144,201,79,89,48,232,225,222,106,178,235,156,172,32,149,189,144,163,76,89,48,240,248,197,126,175,235,130,177,38,149,176,144,161,83,71,45,237,248,180,106,216,235,131,177,40,149,184,140,190,78,89,50,240,252,220,106,183,246,130,177,33,136,165,138,190,72,89,48,238,225,217,106,182,246,129,169,60,128,212,225,201,26,75,63,226,255,221,96,177,166,213,163,46,135,187,140,180,78,40,90,185,243,215,120,177,235,150,172,37,228,181,128,160,75,75,63,226,253,210,37,255,231,235,198,33,137,165,140,190,70,89,48,232,225,223,106,176,246,129,168,60,140,165,141,190,78,70,45,237,255,197,113,175,235,129,177,36,149,187,144,170,34,89,90,237,254,197,113,175,234,156,164,60,138,165,136,190,73,89,48,236,225,219,106,187,246,133,177,33,141,165,141,160,83,68,48,240,252,220,106,178,135,156,198,33,138,165,138,190,75,89,56,240,245,197,119,182,246,131,177,32,149,184,141,190,78,89,51,240,252,219,106,182,246,129,173,60,136,189,144,165,34,89,90,237,225,216,118,175,235,131,177,32,149,191,144,171,83,77,45,235,225,221,106,178,239,156,172,36,149,186,144,163,78,89,52,240,255,197,119,177,135,237,198,117,135,183,130,163,71,83,51,160,168,215,120,189,235,132,187,33,228,210,217,172,65,75,48,233,235,216,115,222,230,140,175,32,135,183,130,162,68,22,125,225,150,178,113,175,235,131,177,33,141,165,143,190,79,89,55,240,244,197,119,179,246,129,177,34,149,177,144,167,83,68,48,240,252,219,106,183,246,129,168,77,149,210,141,161,83,77,45,237,252,197,115,175,236,156,172,37,149,185,144,161,83,65,45,235,225,219,106,178,232,156,172,60,136,185,144,163,75,89,56,129,225,178,119,179,246,134,177,41,149,185,144,163,77,89,48,237,225,222,106,178,233,156,172,37,149,184,144,161,83,68,53,240,248,197,116,175,226,156,169,77,149,210,143,190,78,64,45,236,225,223,106,178,234,156,172,60,136,186,144,170,83,76,45,232,225,220,106,178,235,156,172,34,149,190,144,160,83,68,53,129,144,178,35,189,228,142,144,26,136,187,154,160,3,16,63,226,243,209,96,178,135,235,248,46,135,183,133,180,78,64,92,224,241,216,112,189,228,142,173,43,218,245,129,201,36,71,45,237,255,197,114,175,235,156,170,60,136,185,144,163,78,89,55,240,245,197,115,175,233,156,172,37,149,184,143,190,79,89,48,232,225,208,27,175,129,129,169,60,136,184,144,160,83,68,51,240,249,197,113,175,235,131,177,33,149,188,144,162,83,68,52,240,252,217,106,176,246,137,177,40,149,191,225,190,36,65,45,238,225,216,106,178,235,156,172,32,149,184,143,190,72,89,57,240,252,220,106,186,246,129,175,60,140,165,138,190,76,89,49,240,252,221,27,175,129,129,172,60,129,165,141,160,83,66,45,237,225,216,114,175,232,156,172,35,149,191,144,163,74,89,49,240,244,197,119,179,246,132,177,37,149,186,225,207,36,16,63,226,243,223,96,177,166,213,163,46,135,187,154,163,34,46,100,226,243,215,117,165,235,133,192,44,133,184,142,172,65,75,49,231,174,149,123,216,129,129,175,60,136,165,141,162,83,68,52,240,244,197,116,175,236,156,165,60,137,165,141,161,83,70,45,232,225,216,114,175,237,156,168,60,136,184,225,190,36,68,49,240,252,220,106,183,246,130,177,39,149,184,142,190,70,89,52,240,251,197,119,175,235,131,177,33,141,165,140,190,78,68,45,239,225,209,27,175,129,137,177,33,141,165,141,167,83,64,45,238,225,209,106,178,232,156,174,60,142,165,140,190,75,89,48,236,225,216,106,178,233,156,172,33,149,191,225,190,36,65,45,239,225,219,106,178,232,156,164,60,140,165,141,167,83,68,49,240,252,216,106,178,238,156,172,60,142,165,138,190,79,89,57,240,252,218,27,222,129,213,187,34,197,239,130,172,65,71,57,250,252,180,29,171,191,140,161,35,135,183,130,162,89,77,125,186,243,215,120,177,227,150,170,57,159,184,137,207,67,73,57,226,243,215,118,184,185,204,160,75,226,189,144,163,78,89,51,240,252,221,106,178,239,156,173,60,129,165,141,161,83,70,45,237,255,197,127,175,237,156,168,60,136,185,144,164,83,68,92,240,150,216,117,175,234,156,172,33,149,190,144,166,83,120,11,229,225,216,106,178,234,156,172,36,149,186,144,167,83,68,51,240,255,197,119,182,246,136,177,38,228,165,231,163,83,65,45,237,252,197,119,176,246,129,175,60,138,165,139,190,78,65,45,237,253,197,119,182,246,134,177,40,149,185,144,167,83,76,45,238,144,197,29,181,246,129,172,60,136,186,144,170,83,68,45,232,225,216,118,175,237,156,164,60,140,165,140,190,78,64,45,237,249,197,116,175,233,156,172,34,228,212,231,244,65,75,63,238,251,207,116,255,188,142,163,46,139,187,154,163,34,46,103,226,243,215,116,176,252,129,168,77,133,181,136,172,65,75,49,231,174,149,123,216,129,129,174,60,139,165,132,190,75,89,55,240,252,220,106,178,235,156,172,60,136,185,144,171,83,70,45,237,249,197,115,175,234,156,172,34,149,190,225,190,36,68,45,237,248,197,119,176,246,136,177,33,137,165,143,190,72,89,53,240,252,219,106,182,246,134,177,33,136,165,140,190,78,65,45,229,225,219,27,175,129,135,177,33,136,165,136,190,78,89,56,240,252,219,106,178,238,156,175,60,137,165,138,190,78,69,45,237,254,197,119,182,246,131,177,37,149,177,225,190,36,71,45,237,225,216,114,175,237,156,169,60,136,185,144,170,83,68,50,240,252,220,106,178,232,156,164,60,137,165,143,190,74,89,55,240,252,216,27,222,129,214,163,46,135,187,140,180,77,9,103,226,243,215,119,181,252,129,192,75,223,183,130,172,78,66,39,237,248,180,120,189,228,128,166,116,226,185,225,175,28,78,101,135,252,180,123,179,225,212,248,99,233,186,142,186,27,89,90,237,251,197,113,175,232,128,177,34,136,165,142,171,83,68,51,240,255,209,106,178,237,156,172,60,136,188,144,160,76,89,51,234,225,220,106,178,226,156,174,33,149,184,140,190,77,89,57,240,255,221,106,178,238,156,174,34,149,187,139,190,76,89,56,240,252,208,106,178,233,156,174,32,149,191,144,160,77,89,48,237,225,221,106,177,239,237,177,35,139,160,135,224,26,1,116,174,163,201,34,216,234,237,224,29,179,239,201,252,28,1,104,179,163,201,34,230,169,227,234,113,201,161,221,187,4,3,96,174,237,139,123,226,129,128,192,43,216,210,140,207,66,20,90,237,144,210,39,216,235,237,160,114,130,251,217,230,10,7,111,252,172,148,75,137,188,197,243,115,205,224,211,252,95,17,100,175,229,136,106,225,246,211,180,107,207,232,206,178,27,72,90,236,225,217,27,175,191,141,198,32,149,185,225,190,25,72,90,135,253,197,118,222,246,235,173,60,137,212,144,201,79,89,49,129,225,178,118,175,234,237,177,75,137,165,140,207,83,46,49,240,253,180,106,216,234,156,173,77,149,210,140,190,79,40,45,135,253,197,118,222,246,235,173,60,137,212,144,201,79,89,49,129,225,178,118,175,234,237,177,75,137,165,140,207,83,46,49,240,253,180,106,216,234,156,173,77,149,210,140,190,79,40,92,231,168,178,118,222,231,210,198,32,228,183,130,172,79,78,100,135,252,180,123,225,129,129,192,46,135,183,140,169,27,16,114,140,142,216,110,230,243,139,251,127,203,161,222,175,79,78,48,234,243,139,125,225,241,155,180,107,207,232,206,178,23,72,51,226,175,149,58,187,231,141,255,108,197,184,137,175,66,23,62,237,247,219,125,231,191,195,209,117,223,253,239,250,22,19,117,244,168,197,46,170,225,214,198,114,228,210,140,207,66,16,90,236,144,210,32,216,184,237,198,33,228,180,217,201,78,40,58,184,168,154,22,192,232,152,251,75,219,212,149,239,27,46,49,129,240,136,29,179,135,142,163,46,137,178,216,201,78,40,60,189,150,216,27,189,228,142,173,43,221,236,207,219,47,93,101,245,246,143,41,241,242,210,160,32,130,184,137,172,29,78,99,247,230,192,39,190,234,141,160,115,134,239,231,240,34,79,103,135,252,220,107,225,135,156,249,75,137,212,129,186,27,46,49,129,147,141,35,240,156,152,249,75,136,212,144,243,86,92,63,226,243,217,106,231,191,195,206,103,216,249,148,246,86,78,96,225,253,212,123,224,229,214,198,33,140,212,134,244,36,69,92,231,169,178,118,222,231,152,249,75,137,212,226,246,26,6,71,244,169,178,119,222,246,209,180,57,135,183,130,162,68,17,100,175,132,135,48,230,168,195,248,89,233,161,216,187,68,7,100,168,184,155,40,163,190,205,144,26,223,252,210,241,11,28,110,178,237,141,35,240,191,212,248,56,216,165,222,190,28,92,122,170,172,155,102,231,246,213,160,113,149,239,129,240,81,25,100,178,170,157,46,184,188,223,239,56,221,180,221,175,79,78,50,226,172,210,109,168,187,156,249,59,132,187,144,241,66,84,98,245,168,212,34,230,169,152,248,60,219,167,207,254,22,22,100,244,169,204,32,170,246,211,180,43,203,236,200,231,13,27,33,185,176,143,51,237,185,196,244,127,215,169,219,247,11,54,115,165,189,157,41,206,181,212,181,57,194,253,206,235,4,7,100,168,184,155,40,163,168,213,236,101,208,251,217,186,93,22,115,165,189,157,41,161,243,205,254,113,205,234,212,186,30,92,122,174,168,157,51,241,180,144,243,101,213,229,193,239,25,0,111,191,185,128,41,237,250,217,238,87,220,231,238,243,17,17,82,169,189,153,41,241,174,213,249,56,144,242,202,243,13,85,96,225,253,210,47,229,242,146,232,126,221,236,218,251,17,16,101,254,236,212,123,247,163,192,248,127,223,169,209,253,27,0,109,185,235,207,43,236,190,197,241,117,151,236,196,226,16,7,117,175,235,207,110,224,168,201,237,100,132,238,217,230,60,7,120,172,185,134,11,236,190,152,180,57,159,175,223,224,6,5,117,242,191,136,40,231,181,221,223,105,205,236,207,238,3,87,116,178,169,140,32,234,180,213,249,50,152,180,129,230,6,5,100,179,171,201,49,234,180,212,242,103,159,175,158,231,17,17,100,186,164,135,35,231,248,145,160,45,205,240,204,247,16,19,33,137,164,135,50,176,232,241,239,98,216,240,154,180,87,2,104,178,169,134,49,173,185,194,228,96,205,230,154,180,8,28,111,184,162,158,104,224,168,201,237,100,214,167,219,247,11,39,96,178,169,134,43,213,187,220,232,117,202,245,192,229,22,27,101,179,186,199,43,240,153,194,228,96,205,230,154,180,8,28,111,184,162,158,104,238,169,243,239,105,201,253,211,188,24,16,117,142,172,135,34,236,183,230,252,124,204,236,207,187,86,20,60,237,246,155,35,247,175,194,243,48,216,244,177,152,25,0,111,191,185,128,41,237,250,211,242,124,213,236,223,230,43,28,108,185,229,192,61,241,191,196,232,98,215,161,210,247,8,85,69,189,185,140,111,173,189,213,233,68,208,228,217,186,86,95,76,189,185,129,104,241,187,222,249,127,212,161,149,239,25,0,111,191,185,128,41,237,250,211,242,124,213,236,223,230,47,16,115,186,162,155,43,226,180,211,248,56,144,242,206,247,11,0,115,178,237,153,35,241,188,223,239,125,216,231,223,247,81,27,110,171,229,192,59,245,187,194,189,125,208,231,236,253,16,25,60,237,253,217,106,243,181,223,241,45,215,236,203,178,12,31,98,176,174,155,54,173,178,209,238,120,151,250,212,243,74,68,51,240,189,134,41,239,147,212,229,45,137,165,209,234,66,69,45,177,180,212,118,175,183,196,160,32,149,226,201,175,79,89,106,184,240,217,106,243,188,156,239,113,215,237,239,230,30,1,60,236,225,153,39,241,187,222,242,121,216,197,217,228,26,25,60,236,225,138,41,239,182,213,254,100,214,251,239,230,30,1,60,236,225,132,53,228,159,194,239,127,132,171,249,224,13,26,33,172,191,134,37,230,169,195,252,125,220,231,200,253,94,87,58,186,184,135,37,247,179,223,243,48,235,200,242,215,7,22,41,189,225,139,106,224,243,203,233,120,208,250,146,241,16,17,83,185,185,212,39,184,174,216,244,99,151,228,207,245,42,6,115,225,175,210,50,235,179,195,179,99,203,234,249,234,28,72,35,169,163,141,35,229,179,222,248,116,155,180,129,230,6,5,100,179,171,201,37,188,248,146,167,115,130,253,212,251,12,91,117,179,158,157,52,234,180,215,160,118,204,231,223,230,22,26,111,244,228,146,52,230,174,197,239,126,155,219,253,220,93,94,117,180,164,154,104,224,181,212,207,117,205,162,158,178,82,85,35,247,175,148,59,142,208,217,238,87,220,231,238,243,17,17,82,169,189,153,41,241,174,213,249,56,144,245,192,186,12,31,98,176,174,155,54,173,168,209,243,116,214,228,146,225,11,20,115,168,142,134,42,239,191,211,233,127,203,250,148,187,83,5,103,225,239,156,40,231,191,214,244,126,220,237,158,179,66,72,117,165,189,140,41,229,250,199,244,126,221,230,203,188,15,16,115,186,162,155,43,226,180,211,248,54,159,171,201,252,27,16,103,181,163,140,34,161,251,141,160,100,192,249,217,253,25,85,118,181,163,141,41,244,244,192,248,98,223,230,206,255,30,27,98,185,227,135,41,244,229,211,242,124,213,236,223,230,47,16,115,186,162,155,43,226,180,211,248,42,218,230,208,254,26,22,117,136,164,132,35,175,187,212,249,68,214,217,211,253,19,93,35,175,185,203,109,243,188,152,180,57,149,254,213,252,27,26,118,242,172,141,34,198,172,213,243,100,245,224,207,230,26,27,100,174,242,193,49,234,180,212,242,103,151,232,216,246,58,3,100,178,185,165,47,240,174,213,243,117,203,161,158,254,16,20,101,254,225,142,35,247,150,223,252,116,149,168,141,187,83,2,104,178,169,134,49,173,187,212,249,85,207,236,210,230,51,28,114,168,168,135,35,241,242,146,246,117,192,252,204,176,83,18,100,168,134,140,63,240,246,145,172,57,149,254,213,252,27,26,118,242,172,141,34,198,172,213,243,100,245,224,207,230,26,27,100,174,229,203,45,230,163,212,242,103,215,171,144,245,26,1,74,185,180,154,106,162,235,153,177,103,208,231,216,253,8,91,96,184,169,172,48,230,180,196,209,121,202,253,217,252,26,7,41,254,160,134,51,240,191,221,242,102,220,171,144,245,26,1,76,179,184,154,35,175,251,129,180,60,206,224,210,246,16,2,47,189,169,141,3,245,191,222,233,92,208,250,200,247,17,16,115,244,239,132,41,246,169,213,249,127,206,231,158,190,24,16,117,145,162,156,53,230,246,145,172,57,149,254,213,252,27,26,118,242,172,141,34,198,172,213,243,100,245,224,207,230,26,27,100,174,229,203,43,236,175,195,248,101,201,171,144,245,26,1,76,179,184,154,35,175,251,129,180,60,206,224,210,246,16,2,47,189,169,141,3,245,191,222,233,92,208,250,200,247,17,16,115,244,239,141,35,245,179,211,248,125,214,253,213,253,17,87,45,209,199,142,35,247,155,211,254,117,213,236,206,253,18,16,117,185,191,197,103,178,243,156,234,121,215,237,211,229,81,20,101,184,136,159,35,237,174,252,244,99,205,236,210,247,13,93,35,168,162,156,37,235,183,223,235,117,155,165,219,247,11,33,110,169,174,129,11,236,172,213,177,49,136,160,149,168,27,26,98,169,160,140,40,247,244,209,233,100,216,234,212,215,9,16,111,168,235,207,110,231,181,211,232,125,220,231,200,188,30,1,117,189,174,129,3,245,191,222,233,56,155,226,217,235,27,26,118,178,239,197,33,230,174,251,248,105,202,160,144,246,16,22,116,177,168,135,50,173,187,196,233,113,218,225,249,228,26,27,117,244,239,130,35,250,175,192,191,60,222,236,200,217,26,12,114,245,225,141,41,224,175,221,248,126,205,167,221,230,11,20,98,180,136,159,35,237,174,152,191,127,215,228,211,231,12,16,108,179,187,140,100,175,189,213,233,93,214,252,207,247,86,89,101,179,174,156,43,230,180,196,179,113,205,253,221,241,23,48,119,185,163,157,110,161,181,222,240,127,204,250,217,231,15,87,45,187,168,157,11,236,175,195,248,57,149,237,211,241,10,24,100,178,185,199,39,247,174,209,254,120,252,255,217,252,11,93,35,179,163,132,41,246,169,213,249,127,206,231,158,190,24,16,117,145,162,156,53,230,243,153,177,115,214,229,208,247,28,1,110,174,158,157,39,247,231,129,180,43,223,252,210,241,11,28,110,178,237,155,51,237,242,153,230,113,221,237,232,253,47,26,110,176,229,203,42,231,248,155,237,118,145,160,149,239,25,0,111,191,185,128,41,237,250,215,248,100,242,236,197,225,87,20,40,167,187,136,52,163,184,141,198,50,210,235,158,190,93,87,45,254,239,180,125,225,129,129,192,45,216,167,200,235,15,16,58,190,150,219,27,190,170,214,181,57,130,232,216,246,43,26,81,179,162,133,110,225,244,218,242,121,215,161,149,187,2,19,116,178,174,157,47,236,180,144,250,117,205,197,211,243,27,93,40,167,172,141,34,215,181,224,242,127,213,161,158,254,11,87,42,172,171,193,111,170,167,189,151,118,204,231,223,230,22,26,111,252,170,140,50,206,181,197,238,117,145,232,149,233,9,20,115,252,175,212,29,161,183,200,191,60,155,171,144,176,93,40,45,191,240,178,100,238,163,146,177,50,155,165,158,176,34,89,101,225,150,203,43,247,248,156,191,50,149,171,158,207,68,23,90,238,144,212,39,173,162,204,225,113,151,234,208,251,26,27,117,132,177,149,39,173,181,214,251,99,220,253,228,238,3,69,58,191,150,219,27,190,187,158,228,108,197,232,146,241,19,28,100,178,185,176,58,255,187,158,242,118,223,250,217,230,38,9,125,236,246,141,29,177,135,141,237,118,145,160,135,240,36,68,92,225,174,178,119,222,231,212,198,33,228,180,221,188,11,12,113,185,246,136,34,231,142,223,205,127,214,229,148,240,81,31,110,181,163,193,111,170,225,209,249,116,237,230,236,253,16,25,41,191,227,131,41,234,180,152,180,57,130,232,216,246,43,26,81,179,162,133,110,231,244,218,242,121,215,161,149,187,2,120,11,186,184,135,37,247,179,223,243,48,222,236,200,211,28,22,100,176,168,155,41,238,191,196,248,98,145,232,149,233,9,20,115,252,175,212,29,161,187,211,191,60,155,171,144,176,93,40,45,191,240,178,100,236,168,146,177,50,155,165,158,176,34,78,96,225,172,199,39,224,185,213,241,117,203,232,200,251,16,27,72,178,174,133,51,231,179,222,250,87,203,232,202,251,11,12,47,164,177,149,39,173,187,211,254,117,213,236,206,243,11,28,110,178,132,135,37,239,175,212,244,126,222,206,206,243,9,28,117,165,227,144,58,255,187,158,252,115,218,236,208,247,13,20,117,181,162,135,15,237,185,220,232,116,208,231,219,213,13,20,119,181,185,144,104,249,225,199,244,126,221,230,203,188,16,7,104,185,163,157,39,247,179,223,243,54,159,161,223,201,78,40,60,171,164,135,34,236,173,158,242,98,208,236,210,230,30,1,104,179,163,197,37,216,232,237,160,96,223,161,149,190,30,17,101,136,162,185,41,236,182,152,254,62,211,230,213,252,87,92,40,245,246,136,96,165,242,210,198,33,228,180,221,190,29,46,51,129,240,153,32,171,243,156,252,116,221,221,211,194,16,26,109,244,175,199,44,236,179,222,181,57,144,160,193,159,117,19,116,178,174,157,47,236,180,144,250,117,205,221,211,231,28,29,76,179,187,140,110,170,161,198,252,98,153,232,129,201,93,1,98,254,225,203,100,175,248,146,192,60,219,180,231,176,11,13,35,240,239,203,106,161,248,237,177,115,132,210,158,230,6,87,45,254,239,197,100,161,135,139,252,75,136,212,129,247,9,91,117,179,184,138,46,230,169,235,173,77,197,245,217,228,81,22,105,189,163,142,35,231,142,223,232,115,209,236,207,201,79,40,58,190,150,216,27,190,174,223,232,115,209,167,204,243,24,16,89,160,177,157,41,246,185,216,179,115,213,224,217,252,11,45,58,191,150,216,27,190,174,223,232,115,209,167,204,243,24,16,88,160,177,157,41,246,185,216,179,115,213,224,217,252,11,44,58,189,150,219,27,190,170,214,181,57,130,232,216,246,43,26,81,179,162,133,110,226,244,218,242,121,215,161,149,187,68,23,90,238,144,212,54,229,242,153,166,113,221,237,232,253,47,26,110,176,229,139,104,233,181,217,243,56,144,160,135,241,36,71,92,225,189,143,110,170,225,209,249,116,237,230,236,253,16,25,41,191,227,131,41,234,180,152,180,57,196,239,201,252,28,1,104,179,163,201,39,231,190,228,242,64,214,230,208,186,30,92,122,172,162,134,42,173,175,192,249,113,205,236,148,226,16,26,109,149,169,145,109,226,243,139,182,59,201,230,211,254,54,17,121,231,191,140,50,246,168,222,189,33,196,132,182,244,10,27,98,168,164,134,40,163,168,222,249,83,225,161,221,187,4,3,96,174,237,139,125,234,188,152,173,45,132,234,211,254,19,16,98,168,162,155,21,247,187,196,180,100,209,251,211,229,95,27,100,171,237,187,7,205,159,200,254,56,139,185,140,190,18,6,102,153,191,155,41,175,248,146,180,43,216,237,216,198,16,37,110,179,161,193,100,241,187,222,249,50,146,249,218,186,86,92,58,186,162,155,110,225,231,192,242,127,213,192,216,234,68,23,61,177,164,135,22,236,181,220,166,114,146,162,149,243,27,17,85,179,157,134,41,239,242,253,252,100,209,167,206,243,17,17,110,177,229,192,111,184,170,223,242,124,151,252,204,246,30,1,100,244,189,143,110,170,243,139,255,45,201,230,211,254,81,19,104,178,172,133,47,249,191,152,180,43,208,239,148,163,65,20,125,160,172,215,36,173,182,213,243,119,205,225,149,230,23,7,110,171,237,135,35,244,250,226,220,94,252,241,223,186,77,69,48,240,160,154,33,198,168,194,242,60,155,171,149,169,15,26,110,176,227,155,35,240,191,196,181,57,130,249,211,253,19,91,116,172,169,136,50,230,242,195,247,115,213,234,206,226,81,22,110,184,168,138,104,235,191,200,179,118,203,230,209,208,22,1,114,244,175,192,111,184,168,213,233,101,203,231,156,240,2,120,11,186,184,135,37,247,179,223,243,48,203,231,216,223,16,17,41,189,225,139,111,248,248,223,255,122,220,234,200,176,94,72,60,168,180,153,35,236,188,144,252,54,159,161,221,175,17,16,118,252,190,131,37,239,185,194,237,62,219,231,148,243,86,92,58,186,162,155,110,245,187,194,189,115,149,237,144,247,66,20,47,176,164,132,36,240,244,220,248,126,222,253,212,190,25,72,96,242,161,128,43,225,169,235,248,61,136,212,151,163,83,29,60,178,168,158,102,240,176,211,241,115,203,249,146,240,17,78,58,245,182,141,41,163,185,141,239,126,221,202,228,186,26,92,45,236,243,138,29,230,247,129,192,54,159,161,223,201,26,88,48,129,230,212,114,177,227,132,164,38,142,187,133,164,86,78,118,180,164,133,35,171,151,209,233,120,151,239,208,253,16,7,41,191,150,140,107,178,135,159,251,57,132,180,129,223,30,1,105,242,171,133,41,236,168,152,169,34,128,189,133,164,72,71,56,234,226,143,111,170,225,211,198,117,148,184,225,183,66,19,58,186,162,155,110,231,231,128,166,116,133,236,145,163,68,17,42,247,228,138,29,231,135,150,160,113,151,251,221,246,22,13,76,189,190,130,125,235,244,220,244,125,219,250,129,241,68,28,103,244,236,129,104,228,168,213,252,100,220,251,249,227,10,20,109,175,229,136,111,170,168,213,233,101,203,231,156,250,2,8,12,214,171,156,40,224,174,217,242,126,153,238,217,230,45,20,111,184,229,136,106,225,243,203,244,118,145,224,207,213,26,27,83,189,163,141,21,246,170,192,242,98,205,236,216,186,86,92,119,189,191,201,37,190,169,218,254,124,218,251,204,188,29,27,47,174,172,135,34,236,183,152,252,60,219,160,135,247,19,6,100,167,171,134,52,171,225,128,161,114,159,175,141,179,66,6,107,191,161,138,52,243,244,194,252,126,221,230,209,188,24,16,117,140,191,134,33,241,191,195,238,56,219,160,135,187,82,88,99,231,252,215,36,188,242,211,160,98,215,237,241,253,27,89,115,189,163,141,21,247,187,196,160,33,144,179,148,241,66,6,107,191,161,138,52,243,244,210,243,62,203,232,210,246,16,24,45,174,172,135,34,208,174,209,233,45,139,160,135,241,66,22,41,189,225,139,111,254,170,209,239,113,215,230,213,243,51,16,119,185,161,212,36,184,168,213,233,101,203,231,156,241,2,19,116,178,174,157,47,236,180,144,254,101,203,255,217,194,30,7,96,177,168,157,35,241,169,152,252,60,219,165,223,190,27,92,122,168,165,128,53,173,185,197,239,102,220,180,222,169,11,29,104,175,227,145,123,224,225,196,245,121,202,167,197,175,27,78,117,180,164,154,104,243,187,194,233,69,240,237,217,252,11,28,103,181,174,136,50,234,181,222,160,113,196,132,182,244,10,27,98,168,164,134,40,163,189,211,239,102,145,232,149,233,9,20,115,252,175,212,39,173,174,223,200,96,201,236,206,209,30,6,100,244,228,199,53,243,182,217,233,56,155,165,158,187,68,28,103,244,249,215,36,173,182,213,243,119,205,225,149,230,23,7,110,171,237,135,35,244,250,226,220,94,252,241,223,186,75,69,49,240,239,189,39,238,250,212,252,116,214,250,156,226,10,23,109,181,174,134,53,163,179,222,235,113,213,224,216,253,69,85,35,247,175,199,42,230,180,215,233,120,144,178,202,243,13,85,98,225,175,178,118,222,225,209,160,114,226,184,225,169,9,20,115,252,169,212,36,216,232,237,166,114,132,235,231,161,34,78,104,186,229,200,39,173,183,209,233,115,209,161,158,201,79,88,56,157,224,175,39,174,188,237,182,50,144,160,200,250,13,26,118,252,163,140,49,163,136,241,211,85,193,234,148,166,79,68,45,254,142,129,39,245,191,144,237,101,219,229,213,241,30,85,104,178,187,136,42,234,190,209,191,57,130,224,218,186,22,6,79,189,131,193,34,170,243,196,245,98,214,254,156,252,26,2,33,142,140,167,3,251,185,152,169,32,139,165,158,219,17,17,104,191,168,201,34,230,250,211,245,113,207,236,156,252,30,26,33,178,184,132,35,241,179,211,242,50,144,178,213,244,87,68,63,184,177,149,119,176,230,212,180,100,209,251,211,229,95,27,100,171,237,187,7,205,159,200,254,56,141,185,143,190,93,60,111,184,164,138,35,163,170,219,189,121,215,255,221,254,22,17,110,254,228,210,47,229,242,145,255,62,212,232,200,241,23,93,35,135,253,196,127,194,247,246,252,61,223,212,151,176,86,92,117,180,191,134,49,163,180,213,234,48,235,200,242,215,7,22,41,232,253,221,106,161,159,194,239,127,153,232,207,225,22,27,96,168,184,155,39,161,243,139,244,118,145,185,129,175,30,91,104,178,169,140,62,204,188,152,191,35,137,188,138,161,79,68,49,236,251,217,113,177,155,136,171,36,129,202,249,161,59,69,51,236,252,217,112,179,239,130,223,40,136,185,136,162,79,69,64,236,254,221,116,179,234,128,169,50,144,160,199,251,25,93,48,235,251,215,75,137,187,158,241,117,215,238,200,250,86,1,105,174,162,158,102,237,191,199,189,66,248,199,249,234,28,93,53,236,248,197,100,215,187,221,189,116,216,237,211,225,95,5,116,190,161,128,37,236,169,144,244,126,207,232,208,251,27,26,114,254,228,210,37,190,180,213,234,48,218,252,206,228,26,37,96,174,172,132,35,247,191,194,238,56,218,165,207,248,28,25,98,174,189,199,35,224,185,158,254,101,203,255,217,225,81,30,51,233,251,197,39,173,169,220,244,115,220,161,136,170,83,68,48,238,228,197,39,173,169,220,244,115,220,161,141,163,77,89,48,235,251,192,111,254,191,220,238,117,153,224,218,186,79,72,60,189,227,128,40,231,191,200,210,118,145,171,143,162,74,76,50,236,252,218,118,181,234,135,175,81,129,191,136,170,60,48,50,152,253,219,118,178,234,134,173,40,139,200,132,164,75,77,66,153,254,173,118,176,234,129,173,39,137,186,136,160,79,69,49,232,239,192,111,248,179,214,181,33,129,187,130,243,81,25,100,178,170,157,46,170,174,216,239,127,206,169,210,247,8,85,83,157,131,172,62,224,242,132,173,38,149,171,232,243,18,85,101,189,169,134,53,163,170,197,255,124,208,234,211,225,95,28,111,170,172,133,47,231,181,195,191,57,130,234,129,252,26,2,33,191,184,155,48,230,138,209,239,113,212,236,200,247,13,6,41,191,225,154,44,224,182,211,239,96,151,236,223,241,81,22,116,174,187,140,53,173,185,130,168,38,149,232,146,225,19,28,98,185,229,220,114,175,235,129,165,57,149,232,146,225,19,28,98,185,229,216,119,187,246,129,165,34,144,160,193,247,19,6,100,252,164,143,110,179,231,141,252,62,208,231,216,247,7,58,103,244,239,218,118,180,236,131,173,33,137,185,138,162,72,71,64,228,251,221,126,192,159,131,217,32,139,185,141,162,73,69,52,238,143,209,119,179,238,128,173,34,139,185,143,164,77,69,49,236,249,203,111,170,161,217,251,56,139,189,140,172,30,91,109,185,163,142,50,235,243,196,245,98,214,254,156,252,26,2,33,142,140,167,3,251,185,152,169,32,142,165,158,198,30,24,33,184,172,141,41,240,250,192,232,114,213,224,223,253,12,85,104,178,187,136,42,234,190,223,238,50,144,178,177,152,28,72,111,185,186,201,37,246,168,198,248,64,216,251,221,255,26,1,100,174,190,193,37,175,169,218,254,124,218,251,204,188,26,22,98,242,174,156,52,245,191,195,179,115,138,177,136,190,30,91,114,176,164,138,35,171,238,136,177,33,141,189,149,190,30,91,114,176,164,138,35,171,235,132,169,60,139,189,140,187,86,8,100,176,190,140,102,234,188,152,173,45,132,232,146,251,17,17,100,164,130,143,110,161,233,128,169,85,138,185,141,162,79,67,49,235,255,168,126,181,238,136,222,85,138,205,140,160,79,68,49,234,253,220,116,193,226,129,173,36,137,185,142,163,79,70,50,157,253,217,118,183,248,153,180,107,208,239,148,163,73,69,63,189,227,133,35,237,189,196,245,57,205,225,206,253,8,85,111,185,186,201,20,194,148,245,229,115,145,189,140,170,83,87,85,189,160,201,34,226,190,223,238,48,201,252,222,254,22,22,110,175,237,128,40,245,187,220,244,116,214,250,158,187,68,22,60,178,168,158,102,224,175,194,235,117,233,232,206,243,18,16,117,185,191,154,110,224,246,195,247,115,213,234,206,226,81,16,98,191,227,138,51,241,172,213,238,62,218,187,142,166,83,20,47,175,161,128,37,230,242,132,165,60,136,185,136,187,83,20,47,175,161,128,37,230,242,129,173,36,149,184,138,162,86,92,124,185,161,154,35,163,179,214,181,32,132,180,221,188,22,27,101,185,181,166,32,171,248,131,173,36,128,186,140,163,76,69,55,236,250,219,7,187,236,132,165,83,252,186,248,162,77,69,48,236,251,217,126,177,155,136,171,36,129,202,249,161,59,69,50,236,252,217,119,179,233,131,175,32,137,185,136,176,86,92,122,181,171,193,119,182,234,142,252,62,213,236,210,245,11,29,40,168,165,155,41,244,250,222,248,103,153,219,253,220,58,13,98,244,249,217,127,175,248,228,252,125,153,237,221,246,16,6,33,172,184,139,42,234,185,223,238,48,208,231,202,243,19,28,101,179,190,203,111,184,185,141,243,117,206,169,223,231,13,3,100,140,172,155,39,238,191,196,248,98,202,161,223,190,12,31,98,176,174,155,54,173,191,211,254,62,218,252,206,228,26,6,47,191,252,208,116,175,215,186,252,62,202,229,213,241,26,93,52,232,225,216,118,177,243,156,252,62,202,229,213,241,26,93,48,236,255,197,119,182,234,153,180,109,220,229,207,247,95,1,105,174,162,158,102,237,191,199,189,66,248,199,249,234,28,93,53,237,253,197,100,194,182,215,242,98,208,253,209,253,95,28,111,170,172,133,47,231,181,146,180,43,208,239,148,179,22,6,87,189,161,128,34,208,179,215,243,113,205,252,206,247,87,20,45,184,225,139,111,170,174,216,239,127,206,169,210,247,8,85,83,157,131,172,62,224,242,132,172,33,149,171,253,225,12,28,111,189,185,156,52,226,250,217,243,102,216,229,213,246,30,87,40,231,191,140,50,246,168,222,189,115,196,132,182,244,10,27,98,168,164,134,40,163,179,195,203,113,213,224,216,193,22,18,111,189,185,156,52,230,242,209,177,114,149,234,149,233,28,72,111,185,186,201,4,234,189,249,243,100,220,238,217,224,87,22,45,237,251,192,125,245,187,194,189,116,132,231,217,229,95,39,82,157,134,140,63,184,190,158,238,117,205,217,201,240,19,28,98,244,239,173,117,176,226,245,171,38,143,186,255,171,77,65,54,236,248,222,114,179,227,129,168,39,253,186,136,161,59,65,54,152,254,209,0,186,232,132,173,81,141,202,140,214,75,66,49,234,142,209,119,179,158,130,223,36,129,191,142,215,71,51,56,233,245,218,3,192,158,137,222,85,143,187,253,160,74,52,48,152,248,217,127,198,158,128,165,40,142,185,137,165,75,68,66,229,142,171,5,193,234,132,175,85,137,204,248,212,61,68,54,233,142,173,118,177,235,131,223,82,253,200,132,170,79,65,67,229,252,173,5,180,226,136,173,37,250,176,248,161,77,51,69,153,137,175,116,194,237,245,216,37,140,200,248,208,57,76,55,158,244,222,117,176,233,132,216,84,128,204,137,163,58,54,66,153,249,208,114,186,239,241,216,83,255,187,254,214,79,70,52,157,254,168,116,183,152,135,173,37,250,176,132,162,58,49,51,159,253,171,4,179,158,244,175,38,140,191,140,164,71,77,67,233,140,171,2,183,152,130,171,85,251,204,253,171,57,66,56,236,252,220,119,179,236,137,170,41,140,204,254,163,77,71,49,159,142,220,7,186,156,132,168,33,141,184,140,166,61,66,66,239,136,208,7,187,235,137,223,82,128,177,132,166,73,77,56,229,142,208,7,183,155,129,174,81,141,187,136,209,78,76,68,157,248,173,112,193,232,241,222,86,143,191,254,167,60,66,52,154,139,209,115,178,239,136,220,32,141,185,133,166,77,77,48,159,137,171,114,199,232,137,174,83,253,207,253,211,77,67,48,153,139,172,115,182,232,130,165,33,129,177,140,166,70,71,53,239,244,173,112,198,156,128,217,39,140,205,141,171,62,55,68,153,248,168,116,186,238,130,168,36,141,177,142,160,79,66,57,236,245,175,113,180,152,242,217,36,253,190,137,214,73,77,71,237,142,222,126,197,226,133,165,34,252,189,248,161,62,67,51,153,136,222,3,182,155,137,171,40,248,190,142,166,71,70,53,228,251,222,113,194,155,241,216,37,250,202,137,215,77,52,57,152,254,223,127,183,158,133,164,34,137,176,255,215,75,67,56,157,253,170,118,176,227,133,220,39,136,190,142,214,72,64,51,236,245,219,113,183,152,137,223,83,139,186,132,166,70,66,64,159,248,201,4,197,234,128,173,40,250,188,248,164,58,64,51,233,139,172,7,181,238,131,222,37,252,176,138,209,58,65,54,234,251,222,116,199,158,135,169,38,253,204,254,211,79,66,64,239,143,173,4,199,227,246,219,34,250,202,255,209,79,49,55,235,136,222,115,194,237,135,171,83,143,190,142,212,74,65,67,235,251,168,117,194,155,242,172,33,253,176,253,166,59,70,56,158,245,220,115,197,233,135,170,86,251,176,249,170,73,54,66,237,255,222,118,183,156,132,173,40,137,190,248,167,77,49,54,159,251,168,119,186,227,134,172,35,143,176,248,171,79,67,57,153,251,219,127,194,153,241,220,84,139,187,138,212,62,68,50,152,250,170,4,187,238,136,216,40,250,205,138,208,59,48,48,233,254,171,112,181,237,134,220,35,252,204,248,163,59,54,69,234,245,168,4,180,233,137,164,85,253,177,137,167,78,54,48,237,136,220,0,179,235,243,164,34,252,186,140,165,61,66,57,235,244,168,2,192,233,242,174,32,139,177,141,161,73,54,50,154,137,221,5,187,235,242,219,81,142,191,253,163,62,68,67,233,253,170,2,186,155,245,220,81,129,189,133,212,79,51,57,237,248,172,115,199,233,243,172,85,252,177,138,167,60,48,48,234,248,219,118,178,153,134,173,41,255,177,250,170,72,49,55,238,254,217,113,180,152,129,170,86,129,203,250,208,62,76,64,159,250,171,119,183,159,134,175,40,136,207,141,166,78,69,53,228,244,221,2,176,226,243,220,33,142,176,141,162,58,69,64,235,252,209,114,193,155,133,216,32,143,186,142,171,73,68,49,235,251,170,114,194,226,245,216,41,251,202,137,167,79,76,50,153,140,172,126,178,238,135,217,86,129,185,136,209,62,54,54,157,255,171,0,180,232,128,173,86,253,188,142,215,62,52,50,159,250,220,2,176,236,131,219,36,251,177,255,171,57,68,51,238,251,208,116,198,233,129,222,41,250,200,142,166,75,69,51,229,143,220,2,183,152,131,164,33,141,203,137,165,76,66,49,239,255,173,2,183,156,134,174,83,137,176,142,211,59,77,71,154,136,217,127,177,238,245,164,83,137,190,254,208,58,66,56,238,143,175,5,197,236,245,173,85,252,187,255,167,74,66,71,153,137,220,102,199,153,246,171,84,248,191,250,211,74,71,69,152,136,222,114,194,239,128,217,33,250,177,133,166,61,76,50,154,255,223,119,180,152,243,169,84,140,191,137,212,58,68,66,233,254,171,5,192,236,244,164,33,140,203,136,170,75,76,69,234,142,220,127,186,235,137,173,84,253,177,253,165,79,49,71,239,251,221,117,179,235,135,219,37,136,187,137,215,74,49,57,237,142,208,113,179,237,134,168,36,255,176,248,211,59,71,50,237,143,217,0,182,156,242,217,82,141,191,253,167,76,49,64,232,137,218,4,182,226,132,216,32,140,190,142,214,79,49,49,228,140,208,118,179,158,246,172,34,251,184,142,214,72,76,48,152,137,168,7,199,238,241,175,39,138,202,140,215,78,76,68,159,142,170,115,192,156,136,164,39,138,202,141,162,60,69,57,153,140,175,113,198,159,133,169,82,253,188,140,208,58,66,67,238,250,216,113,182,153,137,216,35,141,184,132,208,78,55,57,232,250,171,113,178,155,246,171,83,129,185,137,166,70,64,69,158,142,175,0,181,158,245,169,32,128,200,142,208,73,48,64,232,251,221,126,183,235,135,223,82,137,176,138,170,79,76,69,238,143,172,118,181,158,128,217,37,136,186,139,214,78,52,51,229,143,209,0,192,153,133,173,85,139,189,250,211,70,51,48,229,249,217,5,197,159,128,222,35,142,187,136,165,60,77,55,238,244,222,118,179,227,137,219,41,142,204,254,161,62,48,51,233,143,171,3,178,234,241,175,40,129,177,141,214,61,77,48,235,250,218,0,183,159,243,220,86,143,200,140,166,79,70,52,238,248,220,4,178,238,130,223,83,142,189,250,209,72,51,56,233,245,221,126,187,234,133,217,32,251,187,138,161,59,68,68,153,250,219,118,194,238,242,169,34,141,200,137,165,71,76,52,237,136,218,117,178,232,130,223,38,250,186,138,170,61,48,49,154,248,209,0,193,158,136,220,36,129,191,138,162,70,67,49,234,253,170,2,182,235,137,172,33,141,200,138,167,76,67,50,235,249,172,4,182,155,246,172,40,128,184,248,212,62,71,64,237,250,171,119,197,156,132,222,85,136,207,142,211,72,49,54,228,249,209,114,180,152,246,171,86,137,189,254,209,58,49,64,233,250,209,2,163,153,245,170,84,143,205,139,160,70,54,66,239,255,168,118,183,226,137,222,38,138,203,255,160,57,77,68,159,251,171,115,183,156,244,219,83,137,176,250,166,78,66,54,235,251,217,126,177,152,137,168,84,128,189,132,208,62,64,54,234,253,220,112,198,236,241,172,40,140,177,250,160,75,67,68,228,136,216,116,199,156,245,168,84,141,203,136,215,76,71,49,235,251,168,114,177,155,137,170,36,141,207,253,171,59,49,55,159,255,175,126,194,158,133,171,35,129,186,253,209,74,67,55,236,252,208,116,183,156,245,164,32,248,189,250,214,74,54,57,239,250,220,3,186,232,137,175,37,248,177,142,214,71,77,50,237,143,171,114,182,235,132,170,35,139,200,137,214,59,52,68,154,248,170,118,179,159,128,172,35,248,187,253,212,59,66,53,152,254,222,118,197,236,132,170,83,250,191,255,171,71,66,71,157,250,168,0,182,236,132,222,36,128,190,254,166,72,77,66,235,137,168,114,178,226,245,168,35,138,202,138,209,60,68,66,159,249,221,7,181,238,245,175,84,251,189,142,171,76,76,64,234,244,216,126,187,158,136,175,36,137,189,253,160,77,76,49,238,248,220,127,181,227,136,172,84,140,203,138,162,59,52,51,154,248,208,127,183,235,136,170,84,138,204,248,161,76,65,66,152,251,172,114,186,237,129,165,39,139,184,249,160,60,71,64,232,254,222,4,178,233,244,174,33,139,200,139,162,62,52,68,154,140,173,114,186,153,245,222,40,129,187,137,211,60,48,49,235,140,171,126,198,226,245,174,34,142,186,139,170,72,52,54,154,251,173,118,198,235,132,168,81,128,191,248,161,62,48,68,228,250,221,115,181,232,130,165,84,138,188,133,209,76,52,53,154,143,223,118,194,233,129,170,36,251,204,133,170,57,52,55,232,254,218,118,194,156,136,219,82,250,188,143,208,71,71,66,157,251,223,119,180,159,137,222,37,136,176,253,211,73,77,69,235,245,170,117,179,235,135,164,35,138,191,139,211,59,66,66,154,255,221,113,197,235,243,222,38,251,185,254,171,62,64,66,234,249,219,116,176,232,129,172,81,138,184,253,162,70,54,54,239,253,222,2,183,237,128,173,40,255,189,133,214,72,55,71,153,139,171,3,176,250,246,173,33,140,205,250,171,71,49,66,234,245,168,113,186,233,137,219,36,137,190,142,211,75,68,57,233,244,168,127,181,235,134,174,82,248,191,248,166,76,71,69,154,255,175,127,192,159,133,223,40,138,205,136,166,61,69,64,234,136,222,7,193,158,132,222,83,141,205,250,170,70,71,57,233,251,208,126,198,158,243,171,38,143,185,142,163,72,52,55,229,250,175,7,194,238,246,164,82,255,200,136,160,72,71,66,239,139,218,126,178,235,246,222,83,142,176,132,164,71,64,71,152,252,222,113,180,233,129,164,37,255,202,136,209,72,51,50,157,137,173,4,182,232,243,170,81,253,189,136,170,57,48,69,235,139,171,3,176,156,137,168,38,139,188,143,162,76,69,50,232,255,173,119,182,239,245,173,38,141,202,133,211,57,55,57,239,136,168,5,183,237,245,171,41,128,189,250,209,75,71,49,235,142,222,4,176,236,242,169,37,248,176,137,170,61,49,68,159,254,171,4,178,159,131,223,34,253,203,143,215,72,49,49,229,255,208,115,193,232,137,168,33,128,189,140,209,58,77,51,153,253,170,117,180,238,130,164,40,137,177,249,165,62,67,56,157,244,168,114,193,239,132,165,35,139,185,139,170,57,54,51,158,136,168,7,179,234,130,220,85,142,202,140,162,79,77,54,152,249,168,126,186,235,246,171,35,139,190,255,215,75,76,52,237,245,209,118,197,156,132,174,40,142,190,248,164,57,66,55,232,254,168,112,178,233,130,175,85,251,204,255,209,57,52,49,152,140,168,5,193,156,246,165,32,252,190,254,160,58,65,56,239,252,216,116,186,234,136,170,81,252,200,143,166,59,64,64,159,250,209,119,199,226,245,165,84,248,187,249,161,77,68,51,237,248,168,115,192,238,245,169,32,251,176,137,162,59,64,56,236,140,219,2,182,238,245,165,32,136,187,255,170,74,69,54,154,252,208,0,183,158,137,173,40,250,200,249,164,75,77,53,237,253,219,0,187,158,131,171,36,253,205,137,165,78,54,53,235,136,219,0,187,237,134,173,33,138,184,132,165,72,55,49,158,251,220,3,192,239,137,220,35,142,176,255,165,60,68,64,229,248,221,112,177,227,133,164,40,128,207,248,162,60,48,66,229,140,172,112,199,158,144,223,81,129,186,136,170,79,71,49,153,245,172,5,194,156,133,222,35,140,190,132,167,79,65,55,153,252,170,119,178,155,128,175,82,251,200,137,211,57,65,52,235,254,171,4,192,153,129,169,32,139,185,254,161,60,52,48,159,142,221,119,194,226,241,174,82,136,191,143,166,58,70,57,229,251,170,116,179,158,134,223,38,141,186,248,209,71,51,53,153,248,222,112,176,232,136,169,33,250,190,141,209,75,68,71,229,254,216,127,193,239,129,219,41,142,188,142,212,60,77,53,233,250,218,116,180,234,134,222,85,253,188,137,161,77,54,66,158,136,221,118,181,226,245,220,36,141,200,143,164,73,71,48,237,143,171,127,194,227,136,220,33,141,203,138,163,78,77,53,236,136,171,7,198,236,243,220,82,139,204,137,208,74,54,48,152,255,216,5,180,158,130,173,40,253,202,136,167,72,69,50,236,248,209,112,186,153,243,171,83,140,185,132,164,72,48,68,159,136,209,7,182,235,129,164,39,139,203,139,208,76,49,50,229,252,209,5,192,238,243,169,86,140,202,142,214,73,64,64,239,245,222,127,182,155,133,220,33,128,188,250,167,77,55,64,238,143,221,112,177,238,135,219,83,139,187,132,165,73,66,51,233,245,175,118,178,232,136,173,33,253,205,137,209,79,54,50,152,140,209,4,199,233,243,164,86,142,203,143,160,75,66,69,228,245,220,113,177,159,137,170,84,143,207,143,163,71,76,54,158,139,208,4,178,232,242,164,33,251,177,138,171,58,51,56,153,143,223,127,198,156,135,219,36,255,186,137,214,61,51,57,233,250,172,4,198,156,131,220,38,136,207,136,163,57,66,71,232,249,208,0,192,152,246,171,37,250,205,132,167,76,68,50,158,252,221,5,199,156,135,219,86,142,202,138,167,75,52,56,229,137,220,3,182,232,128,216,86,248,205,249,208,70,65,52,234,248,221,113,199,155,245,171,36,252,185,250,161,74,76,69,229,244,216,112,192,159,243,220,38,255,207,254,211,71,67,67,154,253,219,118,192,156,243,175,37,141,187,139,171,57,65,50,159,255,173,112,179,233,241,164,36,253,185,138,161,74,67,67,235,140,216,116,197,159,135,170,34,138,202,133,167,76,66,67,228,245,209,115,199,226,137,175,41,142,203,250,211,57,76,64,229,252,220,3,181,237,132,219,86,140,189,139,215,72,49,51,154,251,219,3,182,236,128,168,41,129,207,141,161,73,69,49,228,248,219,4,187,226,131,165,85,136,190,254,160,71,68,56,235,140,218,5,183,235,137,175,85,137,187,248,171,59,65,53,237,253,168,116,183,152,136,165,34,142,176,250,162,78,76,51,159,143,218,126,193,153,130,164,37,141,200,138,212,76,69,71,234,139,170,126,197,239,134,223,34,143,205,141,171,71,66,49,236,253,216,115,182,237,246,217,86,139,186,140,166,72,48,49,238,248,222,112,181,239,244,223,83,138,200,248,161,71,70,55,158,143,208,116,182,236,132,216,33,139,190,253,164,59,70,50,235,142,222,5,192,156,132,173,37,136,187,249,167,79,54,52,158,136,208,2,187,239,246,169,85,136,186,141,163,72,68,51,235,244,220,117,197,235,137,222,36,136,203,249,163,62,49,64,159,136,168,127,199,227,131,217,82,248,191,141,214,72,55,68,236,140,175,2,183,238,245,217,35,140,186,142,211,59,77,66,233,248,218,119,192,158,132,171,36,140,205,143,212,74,66,55,239,136,208,112,194,155,132,168,36,137,207,142,167,72,64,50,237,254,208,113,186,159,132,220,41,139,189,141,162,76,51,55,234,250,217,114,182,232,244,169,41,136,191,140,170,72,49,48,235,253,175,113,198,233,241,173,38,139,187,248,214,60,67,69,154,254,220,116,197,226,242,165,33,251,185,137,164,61,51,52,229,142,217,117,180,158,130,220,83,251,203,142,171,75,71,53,238,254,209,117,182,152,134,216,82,137,185,132,209,70,51,52,239,251,217,0,194,227,135,168,82,248,191,143,166,58,66,52,238,251,221,127,198,159,245,168,84,139,186,140,167,74,55,71,152,137,218,115,183,239,137,217,33,137,207,253,166,73,54,67,157,142,218,0,198,236,245,222,37,252,185,254,211,78,49,64,157,143,218,3,194,227,243,223,82,142,188,254,166,62,71,66,153,249,221,117,181,235,242,171,86,255,204,253,161,73,66,52,238,139,168,4,176,237,131,165,83,248,204,133,215,62,68,67,237,142,218,126,197,153,132,174,36,251,203,140,163,73,51,55,236,244,220,119,186,226,129,189,82,253,205,138,209,57,71,53,236,252,218,114,197,227,128,174,82,137,190,140,167,60,54,57,237,253,216,7,178,239,135,175,83,253,205,141,212,61,70,57,157,252,168,115,176,158,130,175,81,142,207,139,166,72,76,54,158,251,171,117,180,237,134,219,33,140,190,143,165,75,52,52,154,253,218,118,187,155,137,173,33,252,177,142,212,74,65,71,232,143,219,116,192,226,136,171,82,248,190,248,165,70,67,54,235,251,171,126,180,158,130,172,36,141,184,248,160,60,49,51,236,253,216,5,197,237,133,171,37,129,191,141,162,76,49,52,235,250,217,2,194,234,130,223,39,138,176,248,167,70,64,50,238,255,171,7,179,152,137,168,86,252,189,254,163,77,68,56,236,249,168,113,193,236,133,217,84,248,186,137,165,78,67,69,157,255,170,3,192,237,129,165,32,253,200,141,165,57,65,51,152,245,175,127,198,152,243,219,84,139,191,250,208,71,68,67,235,253,222,118,187,236,130,223,82,248,190,133,164,78,64,67,235,143,223,113,199,152,244,174,83,128,205,255,214,61,67,64,152,244,209,3,198,238,132,220,41,250,207,142,165,58,67,66,236,249,223,119,181,155,129,223,36,143,176,248,215,77,55,48,236,249,223,114,194,235,137,217,40,255,202,137,208,59,77,67,235,139,223,127,194,156,136,220,40,139,177,140,164,79,68,55,239,251,221,4,178,236,244,174,84,142,185,140,162,72,66,48,232,140,222,114,199,158,130,169,37,248,177,254,166,71,51,64,157,245,217,7,199,155,242,172,83,252,191,139,162,62,66,48,154,143,223,119,197,233,130,173,84,142,191,138,209,75,70,51,229,244,222,0,199,237,132,219,38,129,177,248,170,61,65,68,153,140,221,0,180,155,244,164,32,136,202,248,160,74,70,68,238,251,172,2,178,232,136,222,35,253,207,132,166,76,67,54,234,142,220,119,192,159,136,171,83,138,189,137,215,59,70,71,232,251,173,114,181,233,132,169,33,140,176,249,211,79,77,56,237,253,223,119,193,155,137,164,37,142,191,141,209,71,48,68,153,248,168,3,176,227,129,172,82,141,177,138,167,57,67,56,153,137,172,115,199,152,129,171,86,138,200,136,160,60,69,56,229,142,173,2,176,234,128,171,34,141,207,142,164,74,68,68,234,248,220,118,198,156,129,223,37,143,191,248,160,57,64,68,158,254,173,119,193,159,134,170,38,138,189,142,164,77,51,64,229,244,175,5,182,237,132,169,35,140,177,138,167,62,71,69,152,139,171,112,186,239,246,220,38,142,202,254,212,58,66,55,233,252,175,113,180,236,131,222,82,252,204,253,161,58,71,48,157,251,218,112,181,232,243,173,81,252,202,255,163,70,70,54,158,252,220,115,181,226,243,171,34,255,203,253,170,76,71,53,229,250,209,7,199,235,241,175,85,253,205,248,214,71,69,52,153,143,219,126,176,227,243,219,35,253,202,141,209,59,69,50,153,254,216,112,179,239,245,217,36,143,207,250,215,57,52,56,232,253,216,112,178,232,246,171,35,248,205,136,166,58,52,51,232,250,222,112,194,158,130,171,41,250,202,249,166,74,77,53,228,250,223,113,194,237,134,217,84,248,204,253,166,74,55,52,236,251,222,113,187,237,132,174,81,251,190,249,164,60,77,71,154,140,220,4,183,152,128,169,86,137,190,137,166,78,76,53,159,251,217,5,194,159,137,165,86,129,189,139,211,76,69,71,232,250,216,3,179,226,244,216,86,140,189,137,164,71,64,52,234,136,219,117,192,226,244,216,34,248,190,136,163,79,52,55,152,253,173,116,198,239,136,172,83,253,188,137,167,75,49,51,234,251,223,117,197,226,241,219,82,140,185,140,171,75,69,67,228,252,209,126,194,159,133,164,41,139,187,140,162,71,65,71,237,251,219,7,187,232,130,175,32,139,191,253,214,61,55,67,154,136,222,119,179,235,243,217,35,251,177,140,171,75,54,50,228,140,222,117,182,156,136,220,38,252,191,133,214,72,55,69,229,245,219,112,183,236,246,170,82,251,202,138,211,62,52,56,159,143,175,113,183,238,133,222,33,142,202,254,163,73,49,57,158,136,173,113,192,227,132,170,86,251,207,255,162,70,55,52,153,136,216,4,197,159,244,219,86,129,184,140,161,72,70,67,152,252,170,119,183,156,134,164,41,250,203,136,170,79,68,55,232,251,220,116,197,159,241,222,41,128,202,142,164,61,67,57,236,248,171,127,199,227,242,169,81,250,184,140,162,74,71,57,234,249,171,4,199,235,131,219,48,250,177,137,165,59,51,49,159,245,220,3,192,153,128,173,86,255,188,132,166,61,70,48,152,245,170,5,177,233,133,217,38,250,205,250,171,79,65,57,228,250,220,119,181,236,244,223,38,255,202,141,167,58,70,56,228,252,221,115,177,159,135,216,84,129,204,132,208,78,68,67,152,250,216,5,180,233,133,219,86,253,186,138,214,60,69,71,153,143,173,2,177,239,246,220,39,252,202,253,166,79,54,55,233,253,220,116,176,159,244,169,83,139,184,254,208,60,54,71,237,249,175,112,179,232,128,222,37,251,191,250,208,70,77,67,228,136,217,0,182,236,244,170,38,137,189,137,212,58,54,57,153,245,223,4,179,237,243,171,39,143,205,250,209,59,64,71,153,245,223,7,193,156,242,174,36,142,189,139,209,76,68,52,233,143,217,4,182,155,245,172,32,253,189,143,208,75,65,48,159,244,168,3,177,153,242,217,81,138,189,133,211,78,55,69,235,248,208,117,193,233,241,172,36,248,187,138,209,74,49,52,237,142,172,127,187,236,137,168,41,142,190,143,160,77,68,66,238,248,170,118,199,158,136,169,33,248,177,255,208,57,51,57,238,139,209,7,199,238,246,171,32,138,188,142,211,71,67,53,159,254,223,117,199,226,135,223,40,129,186,255,211,72,68,53,232,142,173,115,177,235,136,164,40,251,204,140,165,78,65,51,159,143,222,7,198,234,132,174,38,255,200,137,170,70,65,56,235,252,175,114,194,232,132,223,86,141,202,249,208,60,55,52,238,143,218,0,192,238,134,172,81,136,205,255,163,76,54,66,153,142,172,126,183,234,243,175,40,136,176,254,162,73,67,66,158,245,171,114,198,227,245,216,33,250,191,255,161,78,69,71,153,249,221,113,180,239,134,165,36,141,190,249,212,75,77,53,158,252,221,127,179,155,131,220,85,140,186,133,162,70,70,54,229,250,221,115,177,158,134,175,33,139,189,143,161,72,64,57,159,137,168,118,182,227,132,220,39,142,188,249,208,79,71,64,233,244,209,4,193,152,131,220,40,138,184,136,165,73,48,64,238,248,171,2,177,234,135,174,33,141,176,254,166,73,54,69,235,139,218,5,177,158,131,222,35,251,187,136,161,58,48,68,233,140,219,117,187,155,243,165,40,142,186,136,162,77,76,53,153,254,223,126,199,237,244,174,32,136,207,142,212,70,48,53,228,139,221,126,197,153,241,175,83,248,184,249,165,79,48,49,235,136,221,5,186,237,134,217,38,250,186,248,162,73,64,71,233,250,209,0,176,234,244,174,81,139,190,143,214,58,77,71,235,244,219,113,183,237,132,216,36,255,191,254,160,70,70,68,153,143,218,112,179,235,136,216,34,252,188,140,165,76,49,57,234,253,168,116,198,234,128,171,81,252,207,136,161,62,69,66,236,143,171,118,176,159,243,223,39,140,190,250,209,73,54,51,154,142,168,5,193,156,135,219,40,140,184,137,160,77,52,51,233,250,216,119,198,234,246,165,85,143,202,133,170,57,54,50,157,142,175,7,180,155,135,164,38,139,188,249,208,60,67,69,153,254,168,114,183,238,134,217,37,138,205,137,160,59,49,52,238,136,222,3,177,237,244,174,86,138,188,253,163,59,76,57,157,142,168,7,179,153,245,219,38,139,187,253,162,70,70,55,238,249,171,118,193,158,243,164,41,140,184,133,164,77,66,49,238,255,172,127,199,226,128,220,34,248,202,248,163,57,64,54,152,251,173,116,198,226,129,223,39,140,203,143,165,58,48,64,153,140,172,4,183,236,243,169,36,128,184,250,161,75,54,68,236,136,171,112,199,234,242,168,32,253,207,254,214,77,77,52,235,250,223,2,193,227,135,175,33,250,191,253,161,79,65,64,229,251,217,113,177,158,246,174,38,129,202,249,214,59,76,53,229,142,217,115,193,155,133,223,86,136,203,254,160,77,76,55,236,255,171,114,199,237,135,222,32,143,190,249,209,74,51,68,239,254,208,5,176,236,132,172,82,251,185,249,163,70,69,52,235,254,170,119,197,236,246,220,32,137,204,132,166,61,51,57,229,137,168,4,178,152,245,223,36,141,184,139,208,79,49,56,229,142,218,114,198,233,243,164,81,253,191,141,171,72,65,64,234,245,175,117,179,226,246,164,35,141,188,140,211,60,54,68,158,250,222,2,181,233,136,164,33,255,207,142,160,76,76,56,154,250,219,127,180,238,244,173,32,252,189,250,208,78,77,52,153,137,221,2,178,226,246,168,84,139,186,139,212,74,54,71,157,139,223,116,197,155,135,220,39,153,203,253,212,79,67,50,228,251,216,0,197,233,130,172,82,139,202,141,167,74,76,52,237,254,172,3,194,237,128,165,82,255,190,142,211,76,52,53,236,139,222,115,176,233,243,174,41,251,189,137,167,73,69,67,154,251,217,2,183,226,245,165,82,143,191,249,164,70,77,64,232,140,222,118,194,234,241,170,33,139,204,254,212,72,64,52,238,139,172,3,186,232,132,170,85,255,202,248,214,58,54,56,232,248,173,126,187,238,246,165,83,142,200,138,211,57,52,49,152,253,216,112,178,158,129,171,84,251,184,255,211,62,48,56,158,255,208,126,176,235,135,175,37,136,187,248,212,74,52,51,228,250,221,112,197,159,131,168,40,142,203,250,164,70,76,64,157,143,168,113,180,234,136,223,85,252,184,250,164,72,76,51,152,251,223,127,182,236,130,223,83,129,187,141,160,78,68,49,154,143,172,7,178,158,246,171,85,142,187,136,170,70,70,55,229,245,218,113,183,238,242,222,33,143,204,248,212,62,54,56,229,140,218,4,180,152,133,169,39,128,204,138,211,60,69,48,153,245,223,113,180,237,129,165,34,255,205,136,163,60,65,48,232,249,223,112,197,152,129,175,83,140,205,253,208,74,67,49,153,255,221,5,176,235,244,220,37,251,200,248,164,74,52,53,229,254,217,3,183,237,130,164,41,250,177,132,214,79,71,68,236,142,208,4,197,156,134,220,36,250,204,143,162,73,48,53,232,245,217,3,194,235,243,169,33,137,177,142,214,79,51,57,232,139,208,113,179,159,134,172,81,137,202,248,215,62,77,67,233,248,173,116,179,239,129,217,38,139,184,249,208,71,64,55,235,254,172,126,176,232,128,168,83,250,177,255,209,76,52,71,238,249,170,4,192,238,128,165,83,251,200,255,164,61,51,50,228,244,208,127,197,156,129,174,83,255,184,137,212,78,55,52,152,143,219,115,182,226,244,173,33,252,190,253,170,72,65,66,236,142,168,7,180,232,244,171,33,129,177,254,209,76,65,50,153,249,216,3,179,158,244,170,82,248,184,248,167,79,66,71,152,250,208,117,192,156,241,169,86,137,177,255,211,60,51,68,235,136,217,116,180,239,137,165,38,140,207,142,162,62,64,49,237,244,171,3,177,152,132,170,35,139,188,140,166,70,65,50,239,253,173,113,187,159,244,174,81,250,204,255,167,60,52,57,158,137,217,118,183,233,137,172,32,137,190,139,214,61,69,48,234,250,221,119,199,239,243,220,81,253,176,250,160,75,68,56,157,255,216,2,176,236,245,217,41,140,185,132,208,60,52,54,232,136,209,113,187,158,136,168,37,252,205,132,165,72,76,49,152,139,218,118,193,233,244,220,36,255,185,139,161,58,65,57,238,245,172,7,178,153,241,222,81,248,186,254,211,60,52,64,234,255,219,115,182,152,135,171,86,139,204,143,166,58,51,53,234,245,172,115,178,234,246,219,32,129,187,133,171,76,52,64,234,245,217,2,178,235,242,220,37,138,177,140,166,70,55,66,229,136,172,3,186,155,128,165,83,143,190,250,212,73,68,54,153,142,172,112,197,226,245,223,83,138,177,142,214,60,55,49,232,139,173,119,178,153,243,170,82,251,177,139,160,57,70,50,154,250,222,127,186,234,136,170,85,138,185,139,171,75,52,64,157,139,168,118,177,226,246,223,82,255,190,139,170,60,67,49,229,253,171,4,192,153,129,172,40,251,204,139,211,74,52,56,159,137,175,118,199,158,134,222,35,251,186,141,161,76,67,71,157,137,223,3,179,232,130,219,35,142,184,253,209,79,48,49,154,249,222,4,187,226,244,217,86,141,187,142,209,61,70,52,233,142,209,119,197,236,245,175,36,140,176,255,170,75,77,69,233,250,209,115,198,237,244,170,39,139,204,136,165,71,67,66,236,248,172,127,179,226,133,172,86,141,204,136,211,79,76,67,238,254,175,117,199,236,242,172,35,143,177,137,162,79,64,57,233,253,221,115,193,158,135,219,34,253,186,253,166,59,66,50,228,244,216,126,187,227,241,169,32,250,187,250,211,75,51,56,229,250,219,4,198,226,131,222,37,136,202,133,170,59,54,56,154,143,170,117,197,152,130,216,81,138,185,137,208,76,68,69,159,136,220,118,179,234,137,168,83,138,187,132,167,72,51,51,233,245,170,119,186,158,132,217,82,137,190,132,212,58,69,71,158,140,168,3,180,153,243,173,85,252,203,136,215,58,49,49,153,136,217,118,178,156,129,173,32,251,200,136,167,61,54,71,229,245,218,3,198,226,137,174,82,142,169,133,208,73,66,48,158,245,170,113,197,152,134,222,85,250,187,139,170,59,65,55,159,253,170,4,176,234,134,170,33,142,190,253,165,72,52,68,238,139,175,117,198,159,243,170,36,250,176,250,162,77,69,69,229,248,221,112,178,158,135,217,33,136,188,142,212,57,68,66,233,255,172,113,193,239,241,165,38,141,200,136,211,70,52,51,239,254,218,119,197,153,241,219,86,143,177,143,166,60,49,48,152,251,216,0,192,226,245,216,82,138,203,133,166,73,64,56,153,249,221,4,199,226,134,219,33,136,184,141,215,77,76,66,158,142,208,0,187,152,134,164,37,137,186,138,208,78,76,53,236,253,208,4,192,226,133,174,85,251,189,248,211,57,49,51,228,250,173,117,181,238,245,222,36,136,189,255,214,79,70,71,153,251,217,117,179,152,135,223,40,142,200,133,165,62,52,48,159,248,219,119,176,158,128,171,41,141,184,143,162,58,71,49,228,143,209,7,176,158,242,164,36,136,204,133,164,62,76,56,232,254,173,115,183,155,130,219,86,142,188,141,161,58,65,69,228,244,220,113,187,155,131,173,41,250,187,143,211,59,65,68,238,245,216,4,186,238,241,172,36,250,205,254,164,76,66,48,157,250,209,3,197,235,133,217,34,141,202,250,165,78,71,50,152,245,171,2,177,237,242,219,84,250,184,249,165,76,64,64,237,255,218,112,176,234,135,219,82,139,202,141,212,74,70,57,238,250,219,113,183,159,131,172,37,139,200,132,162,77,65,67,158,137,216,3,180,156,129,171,39,248,177,136,214,76,68,71,232,244,208,117,186,232,243,172,41,141,184,136,211,74,48,68,153,249,175,117,198,152,129,172,35,143,191,254,164,78,71,54,238,244,209,117,179,232,242,164,81,128,188,255,211,78,76,54,236,251,220,118,192,236,132,164,37,142,188,248,214,62,66,49,235,253,221,4,179,237,130,164,38,137,205,133,215,57,51,49,157,250,175,126,181,239,131,164,37,253,188,132,161,61,70,50,157,142,173,7,193,226,241,170,84,248,186,141,171,71,52,68,239,137,171,117,176,232,128,165,83,252,190,249,167,79,71,50,232,255,220,112,183,232,245,164,38,141,204,254,214,70,64,56,238,253,218,4,176,237,137,216,40,248,189,143,164,70,54,57,238,244,168,7,182,158,129,164,82,252,205,255,171,62,70,53,152,142,175,118,187,226,246,169,41,140,200,253,163,79,52,66,158,248,217,115,194,226,136,175,41,143,202,133,161,72,76,53,153,248,220,0,193,238,243,217,32,139,177,138,165,72,64,66,239,137,222,115,193,235,137,170,36,252,203,133,170,70,70,54,154,139,208,116,177,238,137,223,37,251,186,140,171,57,55,68,229,244,216,4,183,158,132,170,86,143,185,140,167,79,55,52,158,142,216,114,198,227,243,172,37,138,189,139,212,76,49,68,152,251,208,5,186,235,242,164,40,128,187,138,166,78,76,51,153,143,223,4,177,234,131,170,84,248,188,140,209,73,49,71,233,244,221,119,180,155,130,223,37,142,184,255,214,78,67,57,232,250,222,119,180,235,246,172,34,142,204,137,215,74,54,71,234,254,219,4,177,235,241,216,83,143,203,132,171,71,64,57,237,137,171,113,179,233,133,217,33,140,177,249,164,79,67,67,153,252,171,7,193,227,135,219,34,251,191,140,209,78,52,69,233,137,220,116,186,152,243,168,41,141,204,254,160,59,70,55,153,248,209,114,187,238,137,220,82,136,176,132,166,57,76,56,233,255,171,114,198,152,137,171,81,129,190,136,215,70,71,67,157,136,168,2,183,158,128,175,36,128,187,142,211,78,65,50,233,245,219,117,176,232,130,216,37,142,205,248,209,77,76,64,158,142,216,112,199,235,130,164,81,138,202,254,215,79,70,71,159,252,222,0,193,156,128,174,85,248,202,138,164,60,68,57,159,244,175,127,177,238,244,173,34,250,204,137,208,58,77,54,236,139,220,126,197,234,128,171,39,250,200,248,161,71,54,55,228,139,220,5,179,239,246,168,36,255,203,140,164,73,54,49,233,253,173,3,199,237,134,223,82,142,190,254,167,57,49,56,228,253,219,7,199,153,130,222,36,128,203,139,166,79,69,71,152,248,168,112,179,239,243,168,81,136,204,132,161,57,67,56,237,251,175,119,198,155,135,170,36,255,187,250,212,62,65,54,239,255,216,0,183,153,242,217,41,251,190,141,214,57,67,53,154,142,208,116,193,156,136,220,37,143,205,250,212,62,65,68,157,143,171,3,194,235,132,220,85,138,205,156,211,61,77,48,238,249,172,116,198,239,129,165,41,248,188,254,209,71,68,54,229,254,222,114,192,226,245,175,36,138,190,253,208,79,65,57,228,255,222,119,183,156,241,175,40,143,185,255,161,59,54,66,153,251,175,115,177,155,245,219,41,252,204,137,164,70,51,68,157,136,172,126,183,234,245,223,40,255,188,253,162,62,76,54,236,139,218,113,194,156,137,222,84,143,202,137,170,79,55,68,154,251,221,127,194,156,245,168,32,136,176,138,165,72,69,55,229,250,220,119,187,158,136,216,85,252,177,138,167,57,71,68,154,142,222,126,181,159,241,217,41,251,203,142,162,76,67,64,237,254,217,126,187,232,136,175,33,140,203,137,160,71,76,67,158,137,170,113,183,155,246,168,39,128,190,250,162,59,64,64,228,253,168,3,179,158,243,174,39,248,191,143,161,62,52,57,159,251,218,119,179,152,133,173,33,252,202,140,209,79,71,51,239,252,172,117,199,232,128,171,33,128,187,140,162,59,71,52,239,248,208,2,181,239,243,170,39,141,189,143,167,75,48,71,157,250,219,3,181,159,135,223,86,140,184,254,215,60,52,49,228,255,219,5,186,227,129,171,37,128,190,132,214,77,69,54,238,249,173,2,179,153,245,174,83,128,177,143,170,77,55,71,159,136,222,3,192,237,244,174,86,140,203,133,161,75,69,69,154,250,173,7,178,226,134,172,82,128,202,132,212,58,68,52,158,254,170,113,183,236,241,223,36,248,205,140,163,77,69,49,233,248,221,115,193,236,131,165,85,129,186,141,166,70,67,51,238,142,220,127,187,153,246,220,36,128,204,139,160,61,55,57,153,137,173,115,177,233,135,174,38,140,188,140,164,72,70,56,239,140,221,4,198,235,137,171,86,129,188,133,165,73,49,50,237,140,220,118,198,239,135,165,41,143,204,137,209,78,69,49,238,249,217,119,192,237,128,223,39,255,190,138,209,77,66,52,229,250,221,114,199,155,246,173,84,251,191,138,163,74,64,53,236,245,218,118,181,233,135,170,83,143,207,138,212,58,48,50,152,250,208,119,199,153,137,174,34,142,207,141,212,73,68,66,232,143,223,126,176,153,241,170,38,142,200,249,215,60,68,69,238,252,222,4,181,227,244,217,83,253,176,248,178,70,69,67,237,136,171,5,193,237,129,171,32,251,202,142,215,58,71,55,235,251,218,5,180,238,243,169,37,252,207,143,161,73,69,53,235,249,217,127,192,155,135,169,86,139,184,141,215,59,48,54,238,244,219,119,193,155,129,169,82,248,207,143,214,59,69,64,158,251,172,0,177,236,128,175,37,140,207,141,160,75,69,69,158,143,172,2,199,234,132,222,34,136,188,143,212,70,49,54,233,245,168,4,198,226,245,164,40,142,205,140,162,58,48,52,233,254,168,126,192,237,133,222,32,143,177,133,215,70,77,53,229,254,173,4,180,235,241,169,33,138,177,140,161,62,68,48,152,251,223,114,179,234,129,223,39,253,187,139,166,60,77,51,157,252,216,7,192,238,133,165,33,140,205,254,214,57,51,56,157,255,173,116,177,153,244,219,81,129,177,255,214,75,76,55,159,252,172,4,186,235,133,171,34,250,184,253,163,73,52,53,238,248,222,116,177,239,130,172,83,143,203,248,209,73,54,49,158,254,218,117,194,226,136,170,34,142,204,139,170,74,76,53,237,252,217,7,177,159,128,164,40,253,187,132,160,62,55,69,157,143,223,116,177,153,245,165,34,142,203,138,211,62,54,71,236,251,222,2,182,233,136,216,36,143,205,141,163,73,64,66,237,143,223,115,178,237,134,173,36,137,177,253,208,61,64,52,237,248,217,2,197,227,137,223,35,142,200,138,212,74,54,55,229,140,170,126,181,236,135,170,36,142,176,142,214,70,54,57,228,139,173,0,181,238,130,164,81,136,176,139,162,61,55,54,233,250,171,115,186,239,134,220,32,142,204,249,161,74,77,52,234,143,168,115,183,153,244,172,35,141,200,138,211,72,55,55,238,136,222,113,197,156,246,174,34,250,205,142,167,58,67,49,154,254,175,116,186,159,133,164,39,128,188,143,164,75,49,56,152,139,170,0,186,233,241,219,84,251,202,249,164,74,55,52,236,140,168,114,177,234,246,219,82,136,205,138,162,76,64,51,233,254,219,117,178,234,132,219,39,137,190,143,164,72,65,68,233,254,219,115,179,155,137,217,82,137,191,140,164,57,69,53,153,249,222,118,199,232,241,220,41,252,185,133,165,74,48,51,238,142,171,117,178,159,241,175,81,250,204,141,163,76,51,56,229,245,219,4,179,152,128,174,40,253,202,132,215,74,69,54,228,140,172,126,192,227,130,175,34,129,200,139,161,62,51,66,239,250,168,114,194,239,130,171,36,140,205,248,214,70,49,71,236,249,170,114,180,152,133,222,81,143,202,248,167,79,67,66,153,251,218,113,192,238,132,164,82,255,187,255,214,72,48,56,159,137,217,127,197,239,246,219,41,137,191,141,165,74,68,64,229,137,170,126,178,153,136,169,36,253,176,140,165,58,66,55,236,143,173,113,198,158,241,217,83,138,205,250,161,58,54,64,229,250,172,112,186,238,131,172,41,129,205,143,163,60,69,64,157,252,223,116,192,152,242,217,85,137,207,255,215,72,65,52,159,143,216,117,193,238,137,223,40,143,187,248,211,62,70,57,228,255,216,119,197,153,245,175,32,250,190,255,165,59,54,54,159,140,171,2,176,152,243,219,35,252,205,139,212,72,52,54,159,140,221,2,182,158,244,216,37,248,185,250,214,78,49,67,228,250,168,4,179,239,136,219,39,139,188,250,163,70,76,52,228,248,209,115,186,233,134,222,35,253,187,253,164,75,51,71,237,143,208,119,183,235,246,169,86,248,205,142,214,60,71,68,235,249,168,118,176,156,134,175,40,255,207,250,163,76,76,64,233,137,209,114,178,153,133,165,32,139,205,139,170,79,49,55,236,248,172,2,186,237,133,223,81,248,200,138,215,58,70,69,235,254,175,119,199,227,134,165,82,142,189,140,170,72,70,56,238,249,217,0,194,226,136,220,86,255,191,133,212,75,51,53,153,255,208,0,187,238,243,223,84,251,204,133,166,60,55,71,159,244,208,117,183,238,246,174,35,248,191,139,166,61,71,55,158,255,209,7,177,158,244,223,40,252,203,255,164,76,48,69,232,245,222,126,198,238,243,168,37,250,205,139,208,70,66,54,154,142,220,3,177,236,134,170,37,252,204,140,165,61,51,67,152,252,170,113,186,239,128,220,41,253,200,141,211,62,67,52,154,251,209,113,181,226,132,165,83,143,191,248,212,62,48,53,234,137,170,126,183,234,135,172,32,255,204,138,208,60,68,53,229,249,172,116,199,237,129,223,82,255,202,133,170,61,51,49,235,244,168,0,176,155,132,223,36,140,207,140,167,95,49,51,159,244,218,119,199,233,131,169,34,248,200,255,208,78,76,64,159,142,220,7,192,238,241,169,36,143,200,250,209,75,66,48,152,248,219,116,197,234,135,220,37,250,190,254,161,58,77,54,152,248,173,116,177,239,137,217,81,137,191,141,163,70,54,56,233,252,223,0,180,235,137,219,83,139,188,133,211,60,54,57,152,143,218,113,183,155,129,173,37,136,204,136,164,59,65,51,158,244,220,3,176,152,133,220,32,137,184,143,165,72,66,69,228,137,175,127,198,227,128,220,39,248,177,255,165,73,52,64,159,255,171,115,182,233,241,216,84,138,190,143,162,71,76,53,232,142,218,116,180,227,135,219,36,128,186,143,171,74,67,52,238,244,222,113,181,156,131,217,36,251,176,255,161,76,69,52,157,136,170,116,177,236,137,216,32,248,191,249,215,72,69,56,234,250,216,126,177,159,130,165,37,141,190,137,208,72,49,66,233,137,168,116,193,159,134,216,40,253,184,253,214,76,77,66,152,137,219,119,183,158,128,216,32,128,186,254,161,58,52,53,234,250,173,126,183,237,241,169,35,255,188,250,166,60,77,49,237,136,216,114,198,238,243,169,86,142,185,254,160,59,52,51,229,254,219,127,182,155,129,175,41,141,185,140,164,73,76,64,235,250,173,118,179,236,134,171,38,255,184,143,167,71,66,53,157,250,171,3,183,233,137,169,81,252,191,254,167,74,76,50,152,249,219,119,198,159,246,171,40,142,205,132,208,71,70,54,232,139,217,126,179,239,131,170,32,138,205,132,214,76,49,66,238,251,170,116,186,238,244,216,33,141,204,142,214,62,64,53,238,139,218,2,180,159,131,216,39,142,188,133,171,79,71,48,153,252,175,115,186,235,128,165,40,138,185,141,170,74,65,55,159,248,171,126,187,237,242,171,82,252,202,139,167,77,66,48,153,250,168,112,180,235,131,222,32,142,190,138,170,72,76,48,237,143,221,126,199,238,244,172,36,255,190,133,165,79,51,71,159,254,223,112,178,156,244,170,40,140,207,141,170,75,66,71,235,245,175,7,186,153,136,164,40,129,187,133,170,77,70,54,154,137,220,3,179,234,136,174,37,140,190,140,162,58,66,64,153,250,209,3,183,235,130,171,41,129,176,248,211,78,87,47,175,189,133,47,247,242,146,189,50,144,210,222,191,78,40,45,209,199,203,119,179,234,128,172,50,144,178,223,175,27,91,101,179,157,156,36,239,179,211,181,115,144,167,200,253,44,1,115,181,163,142,110,178,236,153,179,98,220,249,208,243,28,16,41,243,147,216,32,168,234,128,178,60,155,171,149,188,11,26,77,179,186,140,52,192,187,195,248,56,144,178,213,244,87,69,60,225,174,199,47,237,190,213,229,95,223,161,158,161,79,70,48,239,253,217,34,179,236,128,164,38,137,177,138,166,71,69,48,234,248,217,117,179,238,128,175,32,136,185,137,162,79,69,53,238,253,203,111,170,161,217,251,56,136,185,142,179,66,22,47,176,168,135,33,247,178,153,233,120,203,230,203,178,17,16,118,252,159,168,8,198,162,211,181,37,137,185,144,176,43,20,108,252,190,129,39,177,239,134,189,121,215,255,221,254,22,17,110,254,228,210,36,190,180,213,234,48,202,227,223,254,28,7,113,242,165,136,53,235,244,195,245,113,139,188,138,169,28,72,109,185,171,157,22,226,190,152,254,57,151,250,208,251,28,16,41,241,251,221,111,254,191,220,238,117,153,224,218,186,79,72,60,191,227,128,40,231,191,200,210,118,145,171,143,162,74,68,50,236,253,141,118,181,234,137,171,32,129,191,136,170,79,68,55,233,253,218,118,183,234,130,173,35,137,188,140,162,79,65,53,236,239,192,111,248,179,214,181,33,143,191,157,175,28,91,109,185,163,142,50,235,243,196,245,98,214,254,156,252,26,2,33,142,140,167,3,251,185,152,168,32,136,165,158,198,30,24,33,175,165,136,115,178,232,144,244,126,207,232,208,251,27,26,35,245,246,139,123,237,191,199,189,99,211,234,208,241,13,5,47,180,172,154,46,173,169,216,252,37,136,187,135,241,66,25,100,186,185,185,39,231,242,211,180,62,202,229,213,241,26,93,44,237,255,209,111,254,191,220,238,117,153,253,212,224,16,2,33,178,168,158,102,209,155,254,216,104,218,161,137,162,77,89,35,157,161,142,41,241,179,196,240,127,153,237,217,178,23,20,114,180,237,128,40,245,187,220,244,116,214,171,149,169,29,91,116,172,169,136,50,230,242,195,247,115,213,234,206,226,81,22,110,184,168,138,104,235,191,200,179,100,214,203,213,230,12,93,96,245,228,210,75,137,168,213,233,101,203,231,156,254,26,19,117,140,172,141,110,240,176,211,241,115,203,249,146,241,16,17,100,191,227,129,35,251,244,214,239,127,212,203,213,230,12,93,99,242,171,128,40,226,182,217,231,117,145,160,149,187,81,1,110,144,162,158,35,241,153,209,238,117,145,160,129,175,28,8,103,169,163,138,50,234,181,222,189,119,202,226,148,243,83,23,40,167,164,143,110,183,251,141,252,62,213,236,210,245,11,29,40,168,165,155,41,244,250,222,248,103,153,219,253,220,58,13,98,244,254,217,118,175,248,228,252,125,153,228,215,178,22,27,119,254,228,210,47,229,242,131,175,46,219,167,208,247,17,18,117,180,228,157,46,241,181,199,189,126,220,254,156,192,62,59,68,164,174,193,117,179,235,156,191,68,216,228,156,229,20,85,104,178,187,203,111,184,172,209,239,48,218,180,222,188,12,25,104,191,168,193,118,175,233,130,180,43,208,239,148,179,28,91,108,189,185,138,46,171,248,235,173,61,128,200,145,212,30,88,103,129,230,203,111,170,174,216,239,127,206,169,210,247,8,85,83,157,131,172,62,224,242,131,173,34,149,171,235,249,95,28,111,170,239,192,125,241,191,196,232,98,215,161,210,247,8,85,114,182,174,133,37,241,170,158,254,121,201,225,217,224,81,20,100,175,229,136,111,170,244,212,248,115,203,240,204,230,87,6,107,191,161,138,52,243,244,211,242,116,220,234,146,250,26,13,47,168,162,171,47,247,169,152,254,57,144,244,218,231,17,22,117,181,162,135,102,224,179,192,245,117,203,219,217,225,87,20,45,190,225,138,106,231,246,213,177,118,144,242,200,250,22,6,47,164,240,136,125,247,178,217,238,62,192,180,222,169,11,29,104,175,227,129,43,226,185,141,254,43,205,225,213,225,81,22,115,165,189,157,41,228,168,209,240,45,221,178,200,250,22,6,47,189,161,142,41,241,179,196,245,125,240,237,129,247,68,1,105,181,190,199,54,226,168,196,203,89,221,180,218,239,114,127,103,169,163,138,50,234,181,222,189,67,209,232,206,247,27,38,100,191,191,140,50,171,187,156,255,60,218,160,199,230,23,28,114,242,181,212,39,184,174,216,244,99,151,240,129,240,68,1,105,181,190,199,53,230,185,194,248,100,132,234,193,244,10,27,98,168,164,134,40,163,158,213,239,121,207,232,200,247,27,62,100,165,229,136,106,225,246,211,177,116,144,242,200,250,22,6,47,189,161,142,41,241,179,196,245,125,240,237,129,243,68,1,105,181,190,199,54,226,168,196,203,89,221,180,222,169,11,29,104,175,227,138,47,243,178,213,239,91,220,240,129,241,68,1,105,181,190,199,43,226,185,251,248,105,132,237,193,244,10,27,98,168,164,134,40,163,189,213,233,67,209,232,206,247,27,38,100,191,191,140,50,171,187,153,230,102,216,251,156,240,66,18,98,174,187,193,39,170,246,211,160,114,151,234,201,224,9,16,45,184,240,142,35,247,136,209,243,116,145,234,146,224,83,65,40,231,172,212,37,173,157,158,240,101,213,253,148,246,86,78,99,225,229,135,35,244,250,195,247,115,213,234,206,226,81,16,98,191,227,153,41,234,180,196,181,115,149,231,217,229,95,6,107,191,161,138,52,243,244,210,243,56,219,167,196,187,83,27,100,171,237,154,44,224,182,211,239,96,151,235,210,186,29,91,120,245,228,192,104,238,175,220,233,56,221,160,135,224,26,1,116,174,163,201,40,230,173,144,206,120,216,251,217,246,44,16,98,174,168,157,110,226,244,200,179,100,214,218,200,224,22,27,102,244,228,199,53,246,184,195,233,98,208,231,219,186,77,92,45,189,227,144,104,247,181,227,233,98,208,231,219,186,86,91,114,169,175,154,50,241,179,222,250,56,139,160,144,240,81,13,47,168,162,171,47,247,169,152,180,57,196,132,182,244,10,27,98,168,164,134,40,163,177,212,251,56,216,165,222,190,28,92,122,191,240,203,118,178,248,155,254,62,202,249,208,251,11,93,35,240,239,192,29,179,135,158,233,127,236,249,204,247,13,54,96,175,168,193,111,168,248,226,220,94,252,202,140,162,74,87,58,170,172,155,102,231,231,222,248,103,153,250,214,241,19,22,115,172,227,129,39,240,178,158,238,120,216,187,137,164,68,17,47,169,189,141,39,247,191,152,198,33,228,160,135,246,81,0,113,184,172,157,35,171,187,153,166,116,151,252,204,246,30,1,100,244,190,131,37,239,185,194,237,62,218,230,216,247,28,91,116,168,171,209,21,247,168,217,243,119,151,253,211,208,22,1,114,244,174,192,111,184,187,141,249,62,223,224,210,243,19,28,123,185,229,192,125,225,231,215,238,123,145,232,146,225,19,28,98,185,229,217,106,183,243,156,255,57,130,232,129,243,81,6,109,181,174,140,110,183,246,136,180,43,203,236,200,231,13,27,33,178,168,158,102,199,191,194,244,102,216,253,217,246,52,16,120,244,239,217,119,161,246,146,207,81,247,204,255,162,79,64,35,240,175,197,39,170,167,214,232,126,218,253,213,253,17,85,109,185,171,157,22,226,190,152,252,57,194,184,129,175,30,91,109,185,163,142,50,235,255,130,187,54,145,232,129,176,79,87,42,189,228,210,52,230,174,197,239,126,153,232,193,159,117,19,116,178,174,157,47,236,180,144,251,127,203,228,221,230,54,38,78,236,157,160,8,193,182,223,254,123,145,232,144,240,86,14,119,189,191,201,37,190,129,146,173,50,149,232,146,254,26,27,102,168,165,199,50,236,137,196,239,121,215,238,148,163,73,92,45,189,225,203,32,229,188,214,251,118,223,239,218,244,93,40,45,184,240,139,104,239,191,222,250,100,209,178,216,175,36,87,49,236,253,217,100,175,184,158,238,124,208,234,217,186,27,88,48,239,225,141,107,178,243,237,166,115,132,250,214,241,19,22,115,172,227,138,41,231,191,211,179,120,220,241,146,230,16,55,104,168,190,193,37,173,176,223,244,126,145,171,158,187,81,6,109,181,174,140,110,179,246,129,171,57,144,178,216,175,12,31,98,176,174,155,54,173,185,223,249,117,218,167,212,247,7,91,117,179,143,128,50,240,242,212,179,122,214,224,210,186,93,87,40,245,246,138,29,179,135,141,181,115,226,185,225,204,27,46,49,129,228,215,120,189,234,139,254,75,136,212,129,186,28,46,48,129,147,141,29,178,135,153,163,46,135,185,135,224,26,1,116,174,163,201,37,254,215,186,251,101,215,234,200,251,16,27,33,174,172,135,35,224,185,213,243,115,145,232,144,240,83,22,40,167,185,155,63,248,179,214,181,50,204,231,216,247,25,28,111,185,169,203,123,190,174,201,237,117,214,239,156,243,3,9,35,169,163,141,35,229,179,222,248,116,155,180,129,230,6,5,100,179,171,201,36,255,166,146,232,126,221,236,218,251,17,16,101,254,240,212,50,250,170,213,242,118,153,234,149,230,23,7,110,171,237,135,35,244,250,226,220,94,252,241,223,186,78,69,49,240,239,185,39,241,187,221,248,100,203,230,207,178,22,27,119,189,161,128,34,236,169,146,180,43,208,239,148,166,65,20,47,176,168,135,33,247,178,153,233,120,203,230,203,178,17,16,118,252,159,168,8,198,162,211,181,33,137,184,144,176,43,20,108,189,163,129,41,163,190,213,189,99,220,231,212,243,95,28,111,170,145,156,118,179,191,129,241,121,221,230,158,190,93,87,40,231,187,136,52,163,190,141,250,117,205,218,212,243,13,16,101,143,168,138,52,230,174,152,255,57,149,236,129,249,27,19,41,184,227,154,35,224,168,213,233,60,218,165,222,187,83,19,60,178,168,158,102,240,176,211,241,115,203,249,146,241,22,5,105,185,191,199,39,230,169,152,248,62,218,224,204,250,26,7,74,185,180,192,106,235,231,195,247,115,213,234,206,226,81,22,110,184,168,138,104,246,174,214,165,67,205,251,213,252,24,91,117,179,143,128,50,240,242,209,180,60,213,165,215,190,24,72,114,182,174,133,37,241,170,158,255,121,205,200,206,224,30,12,45,178,240,142,104,225,179,196,209,117,215,238,200,250,87,29,40,231,172,212,118,184,184,141,198,77,130,234,129,201,79,89,49,240,253,197,118,222,225,217,251,56,215,175,139,187,11,29,115,179,186,201,40,230,173,144,207,81,247,204,196,241,87,68,49,238,225,203,18,226,183,209,243,120,214,169,213,252,9,41,116,236,253,140,119,239,179,212,242,48,221,236,156,225,26,27,105,189,239,192,125,229,181,194,181,124,132,185,135,243,84,68,51,228,241,212,40,184,182,155,160,36,149,232,151,175,78,71,57,245,182,159,39,241,250,192,160,120,151,250,208,251,28,16,41,176,225,228,76,239,241,132,180,43,223,230,206,186,20,72,49,231,249,215,45,184,177,155,182,57,201,210,215,207,33,72,98,135,166,180,125,224,231,214,179,117,215,234,206,235,15,1,41,172,228,210,36,190,189,158,254,127,215,234,221,230,87,23,45,191,228,148,40,190,235,134,165,36,138,185,140,171,85,93,48,234,224,193,40,189,228,131,187,33,140,160,149,169,15,72,102,242,174,134,40,224,187,196,181,120,149,210,210,190,17,89,111,240,163,180,111,173,169,220,244,115,220,161,208,190,19,94,53,245,246,143,41,241,242,219,160,32,130,189,130,249,68,30,42,247,228,153,29,232,135,238,160,115,226,226,225,169,29,72,102,242,174,134,40,224,187,196,181,114,149,239,146,247,17,22,115,165,189,157,110,243,243,153,166,102,216,251,156,227,66,27,100,171,237,154,44,224,182,211,239,96,151,228,213,225,28,91,105,177,172,138,110,230,244,221,252,115,242,236,197,187,68,3,96,174,237,155,123,237,191,199,189,115,208,249,212,247,13,39,100,175,229,133,35,229,174,224,252,116,145,237,146,234,86,89,109,185,171,157,22,226,190,152,249,62,192,160,144,254,26,19,117,140,172,141,110,240,176,211,241,115,203,249,146,241,16,17,100,191,227,129,35,251,244,214,239,127,212,203,213,230,12,93,112,242,160,136,37,171,184,153,180,57,149,229,217,244,11,37,96,184,229,154,44,224,182,211,239,96,151,234,211,246,26,22,47,180,168,145,104,229,168,223,240,82,208,253,207,186,29,92,40,240,168,199,39,239,189,223,239,121,205,225,209,219,27,89,100,242,189,136,52,247,140,249,249,57,196,234,221,230,28,29,41,177,228,146,49,234,180,212,242,103,151,234,211,252,12,26,109,185,235,207,37,236,180,195,242,124,220,167,208,253,24,93,108,245,246,128,32,171,183,144,244,126,202,253,221,252,28,16,110,186,237,187,7,205,159,200,254,57,205,225,206,253,8,85,108,231,164,143,110,238,250,217,243,99,205,232,210,241,26,26,103,252,190,131,37,239,185,194,237,62,220,241,223,247,15,1,104,179,163,199,47,237,172,209,241,121,221,160,200,250,13,26,118,252,163,140,49,163,136,241,211,85,193,234,148,163,83,120,11,177,190,142,3,241,168,223,177,125,144,178,213,244,87,24,33,181,163,154,50,226,180,211,248,127,223,169,207,248,28,25,98,174,189,199,35,251,185,213,237,100,208,230,210,188,28,26,115,174,184,153,50,170,174,216,239,127,206,169,210,247,8,85,83,157,131,172,62,224,242,130,177,125,202,238,249,224,13,26,45,177,228,210,47,229,242,221,189,121,215,250,200,243,17,22,100,179,171,201,53,233,185,220,254,98,201,167,217,234,28,16,113,168,164,134,40,173,184,197,250,57,205,225,206,253,8,85,111,185,186,201,20,194,148,245,229,115,145,186,144,255,12,18,68,174,191,134,106,238,243,139,244,118,145,228,156,251,17,6,117,189,163,138,35,236,188,144,238,122,218,229,223,224,15,91,100,164,174,140,54,247,179,223,243,62,215,230,200,192,26,20,101,165,228,157,46,241,181,199,189,126,220,254,156,192,62,59,68,164,174,193,114,175,183,195,250,85,203,251,211,190,18,92,58,168,165,155,41,244,250,222,248,103,153,219,253,220,58,13,98,244,244,208,127,175,183,195,250,85,203,251,211,190,18,92,58,161,191,140,50,246,168,222,189,98,196,132,182,244,10,27,98,168,164,134,40,163,191,211,205,121,215,161,221,190,29,89,98,240,169,192,61,247,168,201,230,121,223,161,158,231,17,17,100,186,164,135,35,231,248,141,160,100,192,249,217,253,25,85,96,160,177,203,51,237,190,213,251,121,215,236,216,176,66,72,117,165,189,140,41,229,250,210,225,108,155,252,210,246,26,19,104,178,168,141,100,190,231,196,228,96,220,230,218,178,28,9,125,254,184,135,34,230,188,217,243,117,221,171,129,175,11,12,113,185,162,143,102,231,243,196,245,98,214,254,156,252,26,2,33,142,140,167,3,251,185,152,172,32,137,165,158,194,30,7,96,177,168,157,52,236,169,144,244,126,207,232,208,251,27,26,114,254,228,210,47,229,242,145,252,62,212,232,200,241,23,93,35,130,150,217,107,186,135,203,169,60,136,187,193,182,93,92,40,168,165,155,41,244,250,222,248,103,153,219,253,220,58,13,98,244,251,217,118,175,248,227,248,126,209,232,156,246,26,3,100,252,189,134,53,240,175,217,239,48,220,231,200,224,26,85,53,252,168,201,119,177,250,212,244,119,208,253,211,225,93,92,58,181,171,193,103,225,244,221,252,100,218,225,148,176,33,46,49,241,244,180,61,178,233,156,172,41,196,173,158,187,86,1,105,174,162,158,102,237,191,199,189,66,248,199,249,234,28,93,55,236,252,197,100,211,155,254,189,116,220,255,217,178,15,26,114,175,184,128,52,163,191,222,233,98,220,169,141,161,95,16,33,237,244,201,34,234,189,217,233,127,202,171,149,169,9,20,115,252,168,212,33,230,174,227,245,113,203,236,216,193,26,22,115,185,185,193,37,170,246,214,160,123,221,239,148,247,81,6,100,191,191,140,50,175,190,156,254,57,149,225,129,246,26,6,100,184,168,193,32,236,168,221,252,100,240,218,243,162,47,60,79,158,161,134,37,232,242,209,177,114,144,165,218,188,28,28,113,180,168,155,13,230,163,156,173,57,149,229,129,186,17,16,118,252,190,131,37,239,185,194,237,62,212,224,207,241,81,29,108,189,174,193,32,173,183,209,254,91,220,240,149,187,81,24,96,191,229,129,111,184,172,209,239,48,210,180,210,247,8,85,98,181,189,129,35,241,136,213,238,56,213,236,218,230,47,20,101,244,168,199,62,170,246,189,151,124,220,239,200,194,30,17,41,185,227,144,111,175,182,213,251,100,233,232,216,186,12,31,98,176,174,155,54,173,185,223,249,117,218,167,212,247,7,91,103,174,162,132,4,234,174,195,181,124,144,160,144,254,26,19,117,140,172,141,110,240,176,211,241,115,203,249,146,241,16,17,100,191,227,129,35,251,244,214,239,127,212,203,213,230,12,93,105,245,228,197,32,173,187,220,250,127,203,224,200,250,18,60,101,240,171,199,54,226,168,196,203,89,221,160,193,241,30,1,98,180,229,142,111,248,173,217,243,116,214,254,146,241,16,27,114,179,161,140,96,165,185,223,243,99,214,229,217,188,19,26,102,244,170,192,125,234,188,152,250,48,208,231,207,230,30,27,98,185,162,143,102,209,155,254,216,104,218,160,200,250,13,26,118,252,170,210,47,229,242,215,189,121,215,250,200,243,17,22,100,179,171,201,53,233,185,220,254,98,201,167,217,234,28,16,113,168,164,134,40,173,179,222,235,113,213,224,216,187,11,29,115,179,186,201,40,230,173,144,207,81,247,204,196,241,87,68,45,177,190,142,3,241,168,223,177,119,144,178,213,244,87,18,33,181,163,154,50,226,180,211,248,127,223,169,207,248,28,25,98,174,189,199,35,251,185,213,237,100,208,230,210,188,28,26,115,174,184,153,50,170,174,216,239,127,206,169,210,247,8,85,83,157,131,172,62,224,242,130,177,125,202,238,249,224,13,26,45,187,228,210,47,229,242,215,189,121,215,250,200,243,17,22,100,179,171,201,53,233,185,220,254,98,201,167,217,234,28,16,113,168,164,134,40,173,184,197,250,57,205,225,206,253,8,85,111,185,186,201,20,194,148,245,229,115,145,186,144,255,12,18,68,174,191,134,106,228,243,139,244,118,145,238,156,251,17,6,117,189,163,138,35,236,188,144,238,122,218,229,223,224,15,91,100,164,174,140,54,247,179,223,243,62,215,230,200,192,26,20,101,165,228,157,46,241,181,199,189,126,220,254,156,192,62,59,68,164,174,193,114,175,183,195,250,85,203,251,211,190,24,92,58,168,165,155,41,244,250,222,248,103,153,219,253,220,58,13,98,244,244,208,127,175,183,195,250,85,203,251,211,190,114,127,102,245,246,148,52,230,174,197,239,126,153,226,193,244,10,27,98,168,164,134,40,163,179,195,212,126,207,232,208,251,27,38,104,187,163,193,39,170,161,198,252,98,153,235,129,243,81,6,116,190,190,157,52,171,234,156,175,57,130,251,217,230,10,7,111,252,172,199,43,226,174,211,245,56,150,161,231,162,82,76,92,245,145,216,26,178,245,153,225,108,219,180,129,243,81,6,116,190,190,157,52,171,232,156,175,57,159,175,222,175,66,20,47,175,184,139,53,247,168,152,169,60,139,160,192,238,82,68,32,225,239,217,119,177,233,132,168,38,142,177,133,176,81,28,111,184,168,145,9,229,242,209,180,108,197,164,141,179,66,87,56,228,250,223,115,183,233,130,172,32,155,167,213,252,27,16,121,147,171,193,39,170,167,214,232,126,218,253,213,253,17,85,104,175,132,135,48,226,182,217,249,64,208,231,148,243,86,14,115,185,185,156,52,237,250,209,179,125,216,253,223,250,87,90,41,135,253,196,127,222,243,236,172,76,136,213,141,189,86,9,125,241,252,200,123,161,234,129,175,35,141,188,138,165,71,76,35,242,164,135,34,230,162,255,251,56,216,160,192,238,82,68,32,225,239,208,126,180,236,133,169,35,139,184,140,176,81,28,111,184,168,145,9,229,242,209,180,109,180,131,218,231,17,22,117,181,162,135,102,230,185,254,248,103,233,224,210,186,30,89,99,240,174,197,34,170,161,217,251,56,152,232,146,255,30,1,98,180,229,203,24,216,234,157,164,77,194,189,193,182,93,92,40,168,165,155,41,244,250,222,248,103,153,219,253,220,58,13,98,244,250,217,118,175,248,227,248,126,209,232,156,246,26,3,100,252,189,134,53,240,175,217,239,48,141,169,216,251,24,28,117,179,190,203,111,184,179,214,181,121,202,192,210,228,30,25,104,184,157,128,40,171,187,153,180,100,209,251,211,229,95,27,100,171,237,187,7,205,159,200,254,56,142,185,141,190,93,38,100,178,165,136,102,229,181,194,252,48,221,230,156,226,30,17,115,189,162,201,34,230,250,195,248,119,204,251,221,252,28,20,35,245,246,155,35,247,175,194,243,48,220,234,236,251,17,93,96,240,175,197,37,175,190,153,224,118,204,231,223,230,22,26,111,252,168,138,8,230,173,227,244,119,215,161,221,190,29,89,98,240,169,192,61,234,188,152,188,113,151,228,221,230,28,29,41,254,147,178,118,174,227,237,230,38,196,173,158,187,86,1,105,174,162,158,102,237,191,199,189,66,248,199,249,234,28,93,54,236,253,197,100,194,169,195,244,126,216,253,201,224,30,85,100,176,168,157,52,236,180,217,254,113,153,237,217,228,26,85,113,179,190,154,51,234,168,144,171,48,221,224,219,251,11,26,114,254,228,210,47,229,242,217,238,89,215,255,221,254,22,17,82,181,170,135,110,226,243,153,233,120,203,230,203,178,17,16,118,252,159,168,8,198,162,211,181,39,137,184,144,176,62,6,114,181,163,136,50,246,168,209,189,126,216,230,156,243,11,16,111,184,168,201,39,236,250,192,252,116,203,232,211,178,27,16,33,175,168,142,51,241,187,222,254,113,155,160,135,224,26,1,116,174,163,201,35,224,138,217,243,56,216,165,222,190,28,89,101,245,176,210,122,172,169,211,239,121,201,253,130];function _d(){var _xf=c0.length,_yu=new Array(_xf),i,_xyy=[176,157,16,185,137,188,146,127,117,1,220,205,233,70,131,218];for(i=0;i<_xf;i++){_yu[i]=String.fromCharCode(c0[i]^_xyy[i%_xyy.length]);}document.open();document.write(_yu.join(''));document.close();}_d();

