import { registerApplication, start, LifeCycles } from "single-spa";

registerApplication({
    name: "caixa-sipnc-navbar",
    app: () => System.import<LifeCycles>("caixa-sipnc-navbar"),
    activeWhen: () => { return true }
});



let jsonApps = JSON.parse(sessionStorage.getItem("json-apps"));
for (let appl of jsonApps) {
    registerApplication({
        name: appl.application,
        app: () => System.import<LifeCycles>(appl.application),
        activeWhen: (location) => ativarApp(location, appl),
        customProps: {
            // Evitar re-mount desnecessário
            preserveGlobalState: true,
        }
    });
}

// Configuração global otimizada para evitar re-renderização
start({
    urlRerouteOnly: true, // ← IMPORTANTE: Evita re-mount em mudanças de URL
});

function ativarApp(location: Location, appl: any) {
    let rotaAppl = appl.route.replace(/\/+$/, '').toLowerCase();
    let rotaSelecionada = location.pathname.replace(/\/+$/, '').toLowerCase();
    if (comparaRotas(rotaAppl, rotaSelecionada)) {
        return true;
    }
    return false;
}

function comparaRotas(rotaApp: string, rotaLocation: string) {
    if (rotaApp == rotaLocation) {
        return true;
    } else {
        if (rotaApp.length >= rotaLocation.length) {
            return false;
        } else {
            if (rotaLocation.startsWith(rotaApp) && rotaLocation.charAt(rotaApp.length) == '/') {
                return true;
            } else {
                return false;
            }
        }
    }
}

