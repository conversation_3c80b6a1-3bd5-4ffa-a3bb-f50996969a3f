export declare enum Amplitude {
    NACIONAL = 1,
    PILOTO = 2,
    PREPILOTO = 3
}
export declare enum TipoDocumento {
    "CPF" = 1,
    "NIS" = 2,
    "CNPJ" = 3
}
export declare enum Funcionalidade {
    "PESQUISA_CADASTRAL" = 1,
    "CADASTRO_NIS" = 2,
    "CARTOES_SOCIAIS" = 3,
    "FGTS" = 4,
    "PIS" = 5,
    "SENHA_CIDADAO" = 6,
    "BENEFICIOS_SOCIAIS" = 7,
    "SEGURO_DESEMPREGO" = 8,
    "INSS" = 9,
    "ABERTURA_CONTA" = 10,
    "SAQUE_FGTS" = 11,
    "CADASTRO" = 12,
    "ASSINATURA_ELETRONICA" = 13,
    "CROT" = 14,
    "GERENCIADOR_ATENDIMENTO" = 15,
    "ADESAO_SMS" = 16,
    "SENHA_CONTA" = 17,
    "DEBITO_AUTOMATICO" = 18,
    "ANTECIPA_SAQUE_ANIVERSÁRIO" = 19,
    "PIX" = 20,
    "CREDITO_CONSIGNADO" = 21,
    "FGTS_SISTEMATICA_SAQUE" = 22,
    "AVALIACAO_RISCO" = 24,
    "INVESTIMENTOS" = 26,
    "CREDITO_DIRETO_CAIXA" = 27,
    "CARTAO_CREDITO" = 28,
    "CONVERSAO_CONTA" = 29,
    "SERVICOS_FGTS" = 30,
    "AGENDA_RECEBIVEIS" = 31,
    "TOKEN" = 32,
    "CESTA_SERVICOS" = 33,
    "SEGURANCA_BIOMETRIA" = 34,
    "VALIDA_SENHA" = 35,
    "VALIDA_TOKEN" = 36,
    "CREDITO_SALARIO" = 37,
    "CONTA_JUDICIAL" = 38,
    "GESTAO_LIMITE" = 39,
    "OFERTA_FMP" = 40,
    "AUXILIO_BRASIL" = 41,
    "PRONAMPE_ACEITE" = 42,
    "UPGRADE_CONTA" = 43,
    "DOSSIE" = 44,
    "CONTRATOS" = 46,
    "CONTA_SALARIO_LOTE" = 45,
    "ADESAO_OPTIN" = 47,
    "CONTA_SALARIO" = 48
}
export declare class InfoConsulta {
    tipoDocumento?: TipoDocumento;
    numeroDocumento?: number;
    tipoFuncionalidade?: Funcionalidade;
    sr?: number;
    dired?: number;
    coUnidadeAdministrativa?: number;
    hash: string;
    versao?: number;
    ambiente?: Amplitude;
}
export declare class Trilha {
    infoConsulta?: InfoConsulta;
    constructor();
}
export declare class SrDiredTrilha {
    dired: number;
    sr: number;
    tipoUnidade: string;
    unidade: number;
}
export declare const CODIGO_AGENCIA_VIRTUAL = "codigo-agencia-virtual";
export declare const NUMERO_DOCUMENTO_CPF_CNPJ = "cpfCnpj";
export declare const NUMERO_DOCUMENTO_NIS = "nis";
