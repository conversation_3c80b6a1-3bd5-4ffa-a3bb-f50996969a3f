@import "cx-variables";

/*
	Efeito de aparecimento de dropdown
*/

@keyframes slideIn {
  0% {
    transform: translateY(1rem);
    opacity: 0;
  }

  100% {
    transform: translateY(0rem);
    opacity: 1;
  }

  0% {
    transform: translateY(1rem);
    opacity: 0;
  }
}

@-webkit-keyframes slideIn {
  0% {
    -webkit-transform: transform;
    -webkit-opacity: 0;
  }

  100% {
    -webkit-transform: translateY(0);
    -webkit-opacity: 1;
  }

  0% {
    -webkit-transform: translateY(1rem);
    -webkit-opacity: 0;
  }
}

/*
  Efeito de onda em botões
*/

/* .ripple:not(.disabled) {
  position: relative;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
}

.ripple:not(.disabled):after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, cx-variables.$cx-color-cinza-lighter 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform .5s, opacity 1s;
} */

.ripple-dark {
  @extend .ripple;
}

.ripple-dark:not(.disabled):after {
  background-image: radial-gradient(circle, $cx-color-grafite-darker 10%, transparent 10.01%) !important;
}

.ripple:active:after {
  transform: scale(0, 0);
  opacity: .3;
  transition: 0s;
}

@function set-ripple($color) {
  @if lightness($color) <=50 {
    @return $cx-color-cinza-lighter;
  }

  @else {
    @return $cx-color-grafite-darker;
  }
}

/*
  Efeito ripple em inputs
*/

@keyframes input-ripple {
  0% {
    background-position: 50% 100%, calc(50% - var(--click-el-w, 0px)/2 + var(--click-offset-x, 0px)) 100%;
  }

  100% {
    background-position: 50% 100%, calc(50% - var(--click-el-w, 0px)/2 + var(--click-offset-x, 0px)) 100%;
  }
}

/* SlideIn */

@media (min-width: 992px) {
  .animate {
    animation-duration: 0.3s;
    -webkit-animation-duration: 0.3s;
    animation-fill-mode: both;
    -webkit-animation-fill-mode: both;
  }
}

.slideIn {
  -webkit-animation-name: slideIn;
  animation-name: slideIn;
}


/* Line Loader */

.loading {
  position: relative;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.loading_line_wrapper {
  width: 100%;
  height: 2px;
  position: relative;
  top: 0px;
  left: 0px;
  bottom: auto;
  right: auto;
}

.loading_line {
  position: relative;
  top: 0px;
  left: 0px;
  bottom: auto;
  right: auto;
  width: 100%;
  height: 100%;
  transform-origin: 100% 0%;
  animation: kf_loading_line 2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s infinite;

  .loading_line_inner {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: auto;
    right: auto;
    width: 100%;
    height: 100%;
    background: #000;
    transform-origin: 0% 0%;
  }
}

.loading_line_inner--1 {
  opacity: 0;
  animation: kf_loading_line_inner--1 2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s infinite;
}

.loading_line_inner--2 {
  opacity: 1;
  animation: kf_loading_line_inner--2 2s cubic-bezier(0.645, 0.045, 0.355, 1) 0s infinite;
}

.loading_line_inner {
  position: absolute;
  top: 0px;
  left: 0px;
  bottom: auto;
  right: auto;
  width: 100%;
  height: 100%;
  background-color: #54bbab;
  transform-origin: 0% 0%;
}

@keyframes kf_loading_line {
  0% {
    transform: scaleX(1);
  }

  50% {
    transform: scaleX(1);
  }

  100% {
    transform: scaleX(0);
  }
}

@keyframes kf_loading_line_inner--1 {
  0% {
    transform: scaleX(0);
  }

  25% {
    transform: scaleX(1);
  }

  100% {
    transform: scaleX(1);
  }
}

@keyframes kf_loading_line_inner--2 {
  0% {
    transform: scaleX(0);
  }

  25% {
    transform: scaleX(0);
  }

  50% {
    transform: scaleX(1);
  }

  100% {
    transform: scaleX(1);
  }
}
