<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="utf-8">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <base href="">
  <title>Plataforma Unificada - SIPNC</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <script src="<%= varAmbiente.urlCDN %>/static/js/runtime/runtime-0.13.7.min.js"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/jquery/jquery-3.5.1.min.js" type="text/javascript"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/jquery/dataTables-1.12.1.min.js" type="text/javascript"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/popper-js/popper-1.16.1.min.js" type="text/javascript"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/bootstrap/bootstrap.bundle-4.6.2.min.js"
    type="text/javascript"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/ranecc/ranecc_v601.js" type="text/javascript"></script>
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' blob: https: http: 'unsafe-eval' https: http:  ;
    script-src-elem 'unsafe-inline' https: http: 'unsafe-eval' https: http: ;
    script-src 'unsafe-inline' https: http: 'unsafe-eval' https: http: ;
    connect-src https: http: ws:;
    style-src 'unsafe-inline' https:;
    object-src 'none';
    img-src * 'self' data:;">
  <meta name="importmap-type" content="systemjs-importmap" />
  <script type="systemjs-importmap">
      {
        "imports": {
          "single-spa": "<%= varAmbiente.urlCDN %>/static/js/single-spa/single-spa-5.9.0.min.js",
          "caixa-sipnc-host": "<%= varAmbiente.urlHost %>",
          "caixa-sipnc-core": "<%= varAmbiente.urlCore %>",
          "caixa-sipnc-navbar": "<%= varAmbiente.urlNavBar %>"
        }
      }
  </script>
  <link rel="preload" href="<%= varAmbiente.urlCDN %>/static/js/single-spa/single-spa-5.9.0.min.js" as="script"
    crossOrigin="anonymous">
  <script src="<%= varAmbiente.urlCDN %>/static/js/zone/zone-0.11.3.min.js"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/import-map/import-map-overrides-2.2.0.js"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/systemjs/system-6.8.3.min.js"></script>
  <script src="<%= varAmbiente.urlCDN %>/static/js/systemjs/amd-6.8.3.min.js"></script>
</head>

<body>
  <nav>
    <div id="single-spa-application:caixa-sipnc-navbar"></div>
  </nav>
  <div id="import-map-apps" style="visibility: hidden;"></div>
  <div id="main-content">
    <div id="mensagem-erro" style="visibility: hidden;">
      <div class="row">
        <div class="col-12 col-sm-6 col-xl-8">
          <div class="alert alert-outline-danger">
            <p class="mb-3" id="texto-mensagem-erro"></p>
          </div>
        </div>
      </div>
    </div>
    <div id="mensagem-acesso-negado" style="visibility: hidden;">
      <div class="row">
        <div class="col-12 col-sm-6 col-xl-8">
          <div class="alert alert-outline-warning">
            <p id="texto-mensagem-acesso-negado"></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <import-map-overrides-full show-when-local-storage="devtools" dev-libs></import-map-overrides-full>
  <noscript>
    A plataforma unificada deve ser executada em navegadores que suportem Javascript.
  </noscript>
  <script>
    sessionStorage.setItem("token", "<%= varAmbiente.token %>");
    sessionStorage.setItem("tokenParsed", JSON.stringify(<%= varAmbiente.tokenParsed %>));
    sessionStorage.setItem("cpfCnpj", "<%= varAmbiente.cpfCnpj %>");
    urlApps = "<%= varAmbiente.urlMenuDinamico %>/static/v1/mfes";
    flagLoadApps = ("<%= varAmbiente.isBuscarMFEsDinamicos %>" == "true");
    jsonAppsLocal = JSON.parse(`<%= varAmbiente.listaMFEs %>`);

    new Promise(
      function (fncSucesso) {
        if (flagLoadApps) {
          const listaApps = fetch(urlApps, {
            method: "GET",
            mode: "cors",
            cache: "no-cache"
          })
            .then(response => {
              fncSucesso(response.json());
            })
        } else {
          fncSucesso(jsonAppsLocal);
        }
      }
    ).then(apps => {
      //guardando a lista de apps em JSON para uso futuro:
      sessionStorage.setItem("json-apps", JSON.stringify(apps));

      //criando no DOM as divs onde as aplicações serão montadas:
      apps.forEach(app => {
        let divApp = document.createElement("div");
        divApp.id = "single-spa-application:" + app.application;
        let divMain = document.getElementById("main-content");
        divMain.insertBefore(divApp, divMain.firstChild);
      });

      //criando a tag de scrips de import maps
      let scriptApps = document.createElement("script");
      scriptApps.type = "systemjs-importmap";
      let importApps = {};
      importApps.imports = {};
      for (var i = 0; i < apps.length; i++) {
        importApps.imports[apps[i].application] = apps[i].path;
      }
      scriptApps.innerHTML = JSON.stringify(importApps);
      document.getElementById("import-map-apps").appendChild(scriptApps)
    })
      .then(() => {
        System.prepareImport(true);
      })
      .then(() => {
        System.import('caixa-sipnc-host');
        System.import('caixa-sipnc-core');
      }
      );
  </script>
  <script>
    if (
        window.location.hostname === 'localhost' || 
        window.location.hostname === '127.0.0.1' || 
        window.location.hostname.includes('plataformadev') || 
        window.location.hostname.includes('plataformahm')
    ) {
        
        function limparCpfCnpj(valor) {
            return valor.replace(/[.\-/]/g, '');
        }
  
        function capturarEventoFormulario() {
            const formularioBusca = document.querySelector('form[role="form"]');
            const inputCpfCnpj = document.getElementById('inputSearchCpfCnpj');
            const botaoConsultar = document.getElementById('btnFormBuscaGlobal');
  
            if (formularioBusca && inputCpfCnpj && botaoConsultar) {
                botaoConsultar.addEventListener('click', function(event) {
                    event.preventDefault();  
                    const cpfCnpjLimpo = limparCpfCnpj(inputCpfCnpj.value);
                    if (cpfCnpjLimpo) {
                        sessionStorage.setItem('cpfCnpj', cpfCnpjLimpo);
                        console.log('CPF/CNPJ salvo no sessionStorage:', cpfCnpjLimpo);
                    } else {
                        console.log('O campo CPF/CNPJ está vazio.');
                    }
                });
            } else {
                console.log('Formulário, input ou botão não encontrado.');
            }
        }
  
        window.addEventListener('load', function() {
            setTimeout(capturarEventoFormulario, 100);
        });
    }
  </script>
  
  

</body>

</html>
