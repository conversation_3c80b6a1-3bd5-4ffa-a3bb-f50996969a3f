/* eslint-disable prettier/prettier */
const fs = require("fs-extra");
const path = require("path");
const ambienteLocal = require("../src/environments/environment.local.js");
const ambienteDev = require("../src/environments/environment.dev.js");
const ambienteHm = require("../src/environments/environment.hm.js");
const ambienteProd = require("../src/environments/environment.prd.js");

const env = process.argv[2];
let ambiente;

if (env === "dev") {
  ambiente = ambienteDev;
}

if (env === "hm") {
  ambiente = ambienteHm;
}

if (env === "prd") {
  ambiente = ambienteProd;
}
if (env === "local") {
  ambiente = ambienteLocal;
}

const aplicarVariavelDeAmbiente = (arquivo, nome) => {
  fs.readFile(arquivo, "utf8", (err, data) => {
    if (err) {
      console.error("Erro ao ler o arquivo:", err);
      return;
    }

    // Substituir todas as ocorrências de 'http://localhost:9000' fixa por URL do ambiente
    const conteudo = data.replace(/http:\/\/localhost:9000/g, ambiente.urlCDN);

    fs.writeFile(arquivo, conteudo, "utf8", (err) => {
      if (err) {
        console.error("Erro ao escrever no arquivo:", err);
        return;
      }
      console.log(`URLs do arquivo ${nome} atualizadas!`);
    });
  });
};

aplicarVariavelDeAmbiente(
  path.resolve(__dirname, "../dist/static/navbar/main-es5.js"),
  "main-es5.js"
);
aplicarVariavelDeAmbiente(
  path.resolve(__dirname, "../dist/static/navbar/main-es2015.js"),
  "main-es2015.js"
);
