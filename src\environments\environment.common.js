const common = {
  isBuscarMFEsDinamicos: "false",
  token:
    "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJNRmVKNjVfRC14cU55M1Zta01Ib01WS1NjZlA3S21ZazdtVjBJaEsta0F3In0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uRKqGoxNXhGWIEqz4BxRvyjUGtjpgj_8WdLYiLQMd3HUQmnwnjNtRmKwdbalgat0PnnIpbL-_jONB_yCqLgZHzzraYPlcylvQLRwPuXagnuwjxWSgvjhviwomf6t_o12PMZlRZl6n07GIzTj3Hg2e10j8rR8MBTn6lrkfnfBTUjnibCJksVSBS9Y1m7zS1UdxHdNQ-qJRguMGH3Dzq7XrGG6fM1CBMWYbYwFGzXl8VIsJaZ59by2pfJrAUOZYNLy53Opek1m_zLVD1l3SpafZvcQbEfXpU10SJAP_acvTg6Yv0rRQiZv8_VUMJJdlpHBeyggW2pWjbqGfe1G4kvkqA",
  tokenParsed: JSON.stringify({
    jti: "d8811171-7979-4fdf-8bca-3478fb4541ce",
    exp: 1699050831,
    nbf: 0,
    iat: 1699049931,
    iss: "https://login.des.caixa/auth/realms/intranet",
    sub: "e6df2fc6-a90e-4b0d-8d67-05c0f05b65c8",
    typ: "Bearer",
    azp: "cli-web-pnc",
    nonce: "6ae2ebae-d68d-45ac-a78a-cdfd0faf609c",
    auth_time: 1699049931,
    session_state: "0539e529-a6ef-4951-9757-bb8da3e81f46",
    acr: "1",
    "allowed-origins": ["*"],
    realm_access: {
      roles: [
        "IFX010",
        "MFE_RMC_CAPTACAO",
        "PNCCDO",
        "PNCAPX",
        "PNCAAC",
        "PNCLOG",
        "PNCCEC",
        "PNCAIC",
        "BRJ_SFG_INFORMACOES_FGTS_API",
        "uma_authorization",
        "BRJ_SFG_SAQUE_EMERGENCIAL_API",
        "PNCCCS",
        "PNCCCT",
        "PNCCCR",
        "PNCCCO",
        "PNCCTA",
        "CID_CONSULTA_CARTAO_API",
        "CID_EMITE_CARTAO_API",
        "PNCCCH",
        "PNCCDD",
        "PNCCLM",
        "PNCAPS",
        "PNCCDE",
        "PNCCTR",
        "PNCCDA",
        "PNCAPI",
        "PNCAPD",
        "MFE_RMCPVS",
        "PNCCSC",
        "PNCCSD",
        "MFE_RMCPVT",
        "PNCCSA",
        "MFE_RMCPVV",
        "MFE_RMCPVL",
        "MFE_HOJSEN",
        "MFE_RMCPVO",
        "MFE_RMCPVP",
        "PNCCBE",
        "MFE_RMCPVR",
        "MFE_RMCPVC",
        "PNCCCC",
        "MFE_RMCPVD",
        "PNCCCD",
        "PNCCCA",
        "MFE_RMCPVF",
        "MFE_HOJVCQ",
        "MFE_HOJVCP",
        "PNCCSM",
        "MFE_RMCPVI",
        "PNCAGC",
        "FEC0100",
        "CID_CANCELA_CARTAO_API",
        "PDI003",
        "PNCCSE",
        "PDI001",
        "MTRSDNINT",
        "PNCCRC",
        "PNCCIV",
        "PNCPCA",
        "MTRDOSOPE",
        "PNCCIR",
        "PNCCIP",
        "PNCCIN",
        "PNCCAC",
        "PNCAFG",
        "PNCROT",
        "PNCCAS",
        "PNCCRE",
        "PNCANC",
        "MFE_RMC_360",
        "MFE_RMCPPT",
        "PNCCPX",
        "PNCALT",
        "MFE_HOJVIP",
        "PNCALP",
        "MFE_HOJVAC",
        "PNCCPS",
        "FMP011",
        "BRJ_SFG_SAQUE_DIGITAL_API",
        "PNCCIC",
        "PNCCIA",
        "MFE_RMCVCC",
        "PNCACS",
        "PNCACT",
        "MFE_RMCIIP",
        "PNCACR",
        "PNCACO",
        "PNCATA",
        "PNCACI",
        "GMS_ENVIA_MENSAGEM",
        "PNCACH",
        "PNCADA",
        "PNCCPI",
        "PNCASE",
        "PNCASC",
        "PNCASD",
        "PNCASS",
        "PNCACC",
        "PNCACD",
        "PNCCGF",
        "PNCCON",
        "PNCACA",
        "PNCCGA",
        "offline_access",
        "PNCASM",
        "PNCAAS",
        "GEC403",
        "PNCARE",
        "GEC401",
        "MFE_RMCPII",
        "GEC407",
        "GEC406",
        "PNCAIV",
        "MFE_RMC_NPS",
        "PNCCEP",
        "GEC405",
        "MFE_HOJCSN",
        "PNCAAL",
        "PNCAIP",
        "PNCAAE",
        "PNCAIN",
        "PNCCFG",
        "PNCCNF",
        "PNCAAU",
        "MFE_HOJGST",
      ],
    },
    scope: "openid",
    "2f_cert": "false",
    "co-unidade": "0002",
    cert_obr: false,
    name: "Usuario Teste SIPAN Teste SIPAN",
    preferred_username: "c897998",
    given_name: "Usuario Teste SIPAN",
    clientAddress: "*************",
    family_name: "Teste SIPAN",
    email: "<EMAIL>",
  }),
  cpfCnpj: "22585931153",
};

module.exports = common;
