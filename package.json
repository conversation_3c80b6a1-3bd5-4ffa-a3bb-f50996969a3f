{"name": "caixa-sipnc-host", "version": "2.0.0", "description": "SIPNC - Portal Negócios Caixa", "scripts": {"start": "webpack serve --port 9000 --env isLocal env=local --hot", "build": "webpack --mode=production --env env=dev", "postbuild": "node tools/replaceURL.js dev && node tools/copyIISConfig.js ", "build:pre-dev": "webpack --mode=production --env env=dev", "build:dev": "webpack --mode=production --env env=dev", "postbuild:dev": "node tools/replaceURL.js dev && node tools/copyIISConfig.js ", "build:local": "webpack --mode=production --env env=dev", "postbuild:local": "node tools/copyIISConfig.js", "build:hm": "webpack --mode=production --env env=hm", "postbuild:hm": "node tools/replaceURL.js hm && node tools/copyIISConfig.js", "build:prd": "webpack --mode=production --env env=prd", "postbuild:prd": "node tools/replaceURL.js prd && node tools/copyIISConfig.js ", "lint": "eslint src --ext js,ts,tsx", "test": "cross-env BABEL_ENV=test jest --passWithNoTests", "format": "prettier --write .", "check-format": "prettier --check .", "prepare": "husky install", "build:webpack": "webpack --mode=production", "build:types": "tsc"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "concurrently": "^6.2.1", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "dotenv-webpack": "^8.0.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-important-stuff": "^1.1.0", "eslint-plugin-prettier": "^3.4.1", "html-webpack-plugin": "^5.3.2", "husky": "^7.0.2", "jest": "^27.0.6", "jest-cli": "^27.0.6", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "serve": "^12.0.0", "ts-config-single-spa": "^3.0.0", "typescript": "^4.3.5", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@cvp/utils": "^1.0.0", "@microsoft/applicationinsights-web": "^3.0.2", "@types/jest": "^27.0.1", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "single-spa": "^5.9.3"}, "types": "dist/caixa-sipnc-host.d.ts"}