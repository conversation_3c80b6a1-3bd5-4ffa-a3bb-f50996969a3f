/* eslint-disable prettier/prettier */
//Desenvolvedor: NÃO EDITAR ESTE ARQUIVO caso você esteja desenvolvendo MFEs
//Este arquivo deve ser editado somente caso o host esteja sendo alterado.

const common = require("./environment.common");

const listaMFEs = `[
  {
    "application": "PlataformaCaixa-PosVenda-Vida",
    "route": "/seguridade/vida/pos-venda",
    "path": "https://posvenda.caixavidaeprevidencia.extranet.caixa/vida/CVP-PlataformaCaixa-PosVenda-Vida.js"
  },
  {
    "application": "PlataformaCaixa-PosVenda-Prestamista",
    "route": "/seguridade/prestamista/pos-venda",
    "path": "https://posvenda.caixavidaeprevidencia.extranet.caixa/prestamista/CVP-PlataformaCaixa-PosVenda-Prestamista.js"
  },
  {
    "application": "PlataformaCaixa-PosVenda-Previdencia",
    "route": "/seguridade/previdencia/pos-venda",
    "path": "https://posvenda.caixavidaeprevidencia.extranet.caixa/previdencia/CVP-PlataformaCaixa-PosVenda-Previdencia.js"
  },
  {
    "application": "PlataformaCaixa-Venda-Vida",
    "route": "/seguridade/vida/venda",
    "path": "https://venda.caixavidaeprevidencia.extranet.caixa/vida/CVP-PlataformaCaixa-Venda-Vida.js"
  },
  {
    "application":"PlataformaCaixa-Venda-Previdencia",
    "route":"/seguridade/previdencia/venda",
    "path":"https://venda.caixavidaeprevidencia.extranet.caixa/previdencia/CVP-PlataformaCaixa-Venda-Previdencia.js"
  },
  {
    "application":"PlataformaCaixa-Venda-Prestamista",
    "route":"/seguridade/prestamista/venda",
    "path":"https://venda.caixavidaeprevidencia.extranet.caixa/prestamista/CVP-PlataformaCaixa-Venda-Prestamista.js"
  },
  {
    "application": "caixa-sipnc-assinaturas-des",
    "route": "/assinaturas",
    "path": "https://sipnc-assinaturas-microfront-des.apps.nprd.caixa/main.js"
  }
]`;
const variaveisAmbiente = {
  urlCDN: "https://plataforma.caixavidaeprevidencia.extranet.caixa",
  urlHost:
    "//plataforma.caixavidaeprevidencia.extranet.caixa//caixa-sipnc-host.js",
  urlCore:
    "https://plataforma.caixavidaeprevidencia.extranet.caixa/static/core/caixa-sipnc-core.js",
  urlNavBar:
    "https://plataforma.caixavidaeprevidencia.extranet.caixa/static/navbar/main-es5.js",
  urlMenuDinamico: "https://plataforma.caixavidaeprevidencia.extranet.caixa",
  listaMFEs: listaMFEs,
  isBuscarMFEsDinamicos: "false",
  ...common,
};
module.exports = variaveisAmbiente;

/*
  urlCDN: endereço do CDN;
  urlHost: endereço do host;
  urlCore: endereço do core;
  urlNavBar: endereço do navbar;
  urlMenuDinamico: endereço do backend do menu dinâmico;
  listaMFEs: lista de MFEs a serem carregados pelo host;
  isBuscarMFEsDinamicos: flag que indica se a lista de MFEs a serem carregados virá da variável "listaMFEs" ou
                         do backend do menu dinâmico;
*/
